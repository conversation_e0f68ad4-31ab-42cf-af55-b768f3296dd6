"""
OPTIMIZED CHAT INTERFACE
Professional chat system that automatically detects and uses the best available model.

Features:
- Auto-detects sequential training models (base → reasoning → final)
- Intelligent model selection based on input type
- Optimized generation with proper context handling
- Clean, professional interface

Usage:
  python chat_optimized.py                    # Interactive chat
  python chat_optimized.py --prompt "text"    # Single prompt
  python chat_optimized.py --model path.json  # Specific model
"""
import sys
import os
import argparse
from typing import Optional, List, Tuple

# Add current directory to path
sys.path.append('.')

from models.generator import Generator
from nlp.tokenizer import Tokenizer


class ModelManager:
    """Manages model loading and selection."""
    
    def __init__(self):
        self.tokenizer = self._load_tokenizer()
        self.models = self._discover_models()
        self.active_model = self._select_best_model()
    
    def _load_tokenizer(self) -> Tokenizer:
        """Load tokenizer."""
        tokenizer = Tokenizer()
        
        if os.path.exists("data/vocab.txt"):
            tokenizer.load("data/vocab.txt")
            return tokenizer
        else:
            raise FileNotFoundError("Vocabulary not found. Run training first.")
    
    def _discover_models(self) -> dict:
        """Discover available trained models."""
        models = {}
        model_files = [
            ("final", "models_out/final_model.json"),
            ("reasoning", "models_out/reasoning_model.json"),
            ("base", "models_out/base_language_model.json"),
            ("legacy_neural", "models_out/generator_final.json"),
            ("legacy_ngram", "models_out/simple_model.json")
        ]
        
        for model_type, path in model_files:
            if os.path.exists(path):
                models[model_type] = path
        
        return models
    
    def _select_best_model(self) -> Tuple[str, str]:
        """Select the best available model."""
        # Priority order: final > reasoning > base > legacy
        priority = ["final", "reasoning", "base", "legacy_neural", "legacy_ngram"]
        
        for model_type in priority:
            if model_type in self.models:
                return model_type, self.models[model_type]
        
        raise FileNotFoundError("No trained models found. Run training first.")
    
    def load_model(self, model_path: Optional[str] = None) -> Generator:
        """Load the specified or best available model."""
        if model_path:
            model_type = "custom"
            path = model_path
        else:
            model_type, path = self.active_model
        
        print(f"🔄 Loading {model_type} model: {path}")
        
        # Handle different model types
        if "simple" in path or "ngram" in model_type:
            # Legacy N-gram model
            from train import SimpleNGramModel
            model = SimpleNGramModel(self.tokenizer.get_vocab_size())
            model.load(path)
            print(f"✅ Loaded N-gram model")
            return model, "ngram"
        else:
            # Neural model
            vocab_size = self.tokenizer.get_vocab_size()
            
            # Determine model dimensions based on type
            if model_type in ["final", "reasoning"]:
                embed_dim, hidden_dim = 64, 128  # Sequential training dimensions
            elif model_type == "base":
                embed_dim, hidden_dim = 64, 128  # Base model dimensions
            else:
                embed_dim, hidden_dim = 64, 128  # Default dimensions
            
            model = Generator(
                vocab_size=vocab_size,
                embed_dim=embed_dim,
                hidden_dim=hidden_dim,
                num_layers=1
            )
            model.load(path)
            print(f"✅ Loaded neural model")
            return model, "neural"


class OptimizedChatAgent:
    """Optimized chat agent with intelligent model handling."""
    
    def __init__(self, model_path: Optional[str] = None):
        self.model_manager = ModelManager()
        self.model, self.model_type = self.model_manager.load_model(model_path)
        self.tokenizer = self.model_manager.tokenizer
        
        print(f"🤖 Chat agent ready! Using {self.model_type.upper()} model.")
        self._print_model_info()
    
    def _print_model_info(self):
        """Print information about the loaded model."""
        model_type, model_path = self.model_manager.active_model
        
        capabilities = {
            "final": "🎯 Full reasoning with quality refinement",
            "reasoning": "🧠 Logical reasoning capabilities",
            "base": "📚 Basic language understanding",
            "legacy_neural": "⚡ Legacy neural model",
            "legacy_ngram": "📊 Statistical N-gram model"
        }
        
        capability = capabilities.get(model_type, "🤖 Custom model")
        print(f"📋 Model Capability: {capability}")
    
    def generate_response(self, user_input: str, max_length: int = 25) -> str:
        """Generate intelligent response based on input type."""
        try:
            # Detect input type for optimized handling
            input_type = self._detect_input_type(user_input)
            
            # Tokenize input
            input_tokens = self.tokenizer.encode(
                user_input, 
                add_bos=True, 
                add_eos=False, 
                max_len=20
            )
            
            if not input_tokens:
                return "I didn't understand that. Could you try again?"
            
            # Generate based on model type
            if self.model_type == "ngram":
                response_tokens = self._generate_ngram(input_tokens, max_length)
            else:
                response_tokens = self._generate_neural(input_tokens, max_length, input_type)
            
            # Decode and clean response
            response_text = self.tokenizer.decode(response_tokens)
            response_text = self._clean_response(response_text, user_input, input_type)
            
            return response_text if response_text else "I'm thinking..."
            
        except Exception as e:
            return f"I encountered an issue: {str(e)[:50]}..."
    
    def _detect_input_type(self, text: str) -> str:
        """Detect the type of input for optimized generation."""
        text_lower = text.lower()
        
        if any(keyword in text_lower for keyword in ['given:', 'premise:', 'suppose:']):
            return "reasoning_premise"
        elif any(keyword in text_lower for keyword in ['if ', 'when ', 'whenever']):
            return "reasoning_conditional"
        elif any(keyword in text_lower for keyword in ['what', 'why', 'how', 'who', 'where', '?']):
            return "question"
        elif any(keyword in text_lower for keyword in ['hello', 'hi', 'hey', 'good morning']):
            return "greeting"
        else:
            return "general"
    
    def _generate_ngram(self, input_tokens: List[int], max_length: int) -> List[int]:
        """Generate using N-gram model."""
        full_sequence = self.model.generate_sequence(input_tokens, max_length=max_length)
        return full_sequence[len(input_tokens):]  # Return only continuation
    
    def _generate_neural(self, input_tokens: List[int], max_length: int, input_type: str) -> List[int]:
        """Generate using neural model with type-aware parameters."""
        # Adjust generation parameters based on input type
        if input_type.startswith("reasoning"):
            temperature = 0.7  # More focused for reasoning
            max_length = min(max_length, 20)  # Shorter for reasoning
        elif input_type == "question":
            temperature = 0.8  # Balanced for questions
            max_length = min(max_length, 30)  # Longer for explanations
        else:
            temperature = 0.9  # More creative for general chat
        
        # Generate using the model's sample method
        response_tokens, _ = self.model.sample(
            prompt_ids=input_tokens,
            max_len=max_length,
            temperature=temperature
        )
        
        return response_tokens
    
    def _clean_response(self, response: str, user_input: str, input_type: str) -> str:
        """Clean and format the response."""
        # Remove special tokens
        response = response.replace('<BOS>', '').replace('<EOS>', '').replace('<PAD>', '').strip()
        
        # Handle reasoning responses
        if input_type.startswith("reasoning"):
            if user_input.lower().startswith('given:') and not response.startswith(('Therefore:', 'Thus:', 'So')):
                response = f"Therefore: {response}"
            elif user_input.lower().startswith('if ') and not response.startswith(('then', 'Then')):
                response = f"then {response}"
            elif user_input.lower().startswith('premise:') and not response.startswith(('Therefore:', 'Contradicts:')):
                response = f"Therefore: {response}"
        
        # Clean up extra whitespace and artifacts
        response = ' '.join(response.split())
        
        # Remove common artifacts
        artifacts = ['<UNK>', 'undefined', 'null', '...']
        for artifact in artifacts:
            response = response.replace(artifact, '').strip()
        
        return response
    
    def chat_loop(self):
        """Interactive chat loop."""
        print("\n🤖 Optimized NLP Reasoning Chat")
        print("=" * 50)
        print("✅ Intelligent model selection and generation")
        print("✅ Reasoning-aware response formatting")
        print("✅ Type 'help' for examples, 'quit' to exit")
        print("-" * 50)
        
        while True:
            try:
                user_input = input("\n👤 You: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'bye']:
                    print("👋 Goodbye! Thanks for chatting!")
                    break
                elif user_input.lower() == 'help':
                    self._show_help()
                    continue
                elif user_input.lower() == 'info':
                    self._show_model_info()
                    continue
                elif not user_input:
                    continue
                
                # Generate and display response
                response = self.generate_response(user_input)
                print(f"🤖 Bot: {response}")
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    def _show_help(self):
        """Show help examples."""
        print("🔍 Try these examples:")
        print("\n🧠 Reasoning:")
        print("  • 'Given: A person is running in the park'")
        print("  • 'If it rains heavily today'")
        print("  • 'Premise: All cats are animals'")
        print("\n❓ Questions:")
        print("  • 'What is artificial intelligence?'")
        print("  • 'Why is the sky blue?'")
        print("  • 'How do computers work?'")
        print("\n💬 General:")
        print("  • 'Hello, how are you?'")
        print("  • 'Tell me about space exploration'")
        print("  • 'I think programming is interesting'")
        print("\n🔧 Commands:")
        print("  • 'info' - Show model information")
        print("  • 'quit' - Exit chat")
    
    def _show_model_info(self):
        """Show detailed model information."""
        print("\n📊 Model Information:")
        print(f"  Model Type: {self.model_type.upper()}")
        print(f"  Vocabulary Size: {self.tokenizer.get_vocab_size()}")
        print(f"  Available Models: {list(self.model_manager.models.keys())}")
        
        model_type, _ = self.model_manager.active_model
        if model_type in ["final", "reasoning", "base"]:
            print(f"  Training Stage: Sequential ({model_type})")
        else:
            print(f"  Training Stage: Legacy")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Optimized NLP Chat Interface")
    parser.add_argument("--prompt", type=str, help="Single prompt mode")
    parser.add_argument("--model", type=str, help="Specific model file")
    
    args = parser.parse_args()
    
    try:
        # Create optimized chat agent
        agent = OptimizedChatAgent(model_path=args.model)
        
        if args.prompt:
            # Single prompt mode
            print(f"\n👤 Prompt: {args.prompt}")
            response = agent.generate_response(args.prompt)
            print(f"🤖 Response: {response}")
        else:
            # Interactive chat mode
            agent.chat_loop()
            
    except FileNotFoundError as e:
        print(f"❌ {e}")
        print("💡 Run training first:")
        print("   python train_optimized.py --full --level balanced")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
