"""
Sampling utilities for text generation.
Implements top-k, nucleus (top-p), and other sampling strategies.
"""
import sys
import os
import random

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.math_ops import *


def top_k_sampling(probs, k):
    """
    Apply top-k sampling to probability distribution.

    Args:
        probs: Probability distribution (list of floats)
        k: Number of top tokens to keep

    Returns:
        Modified probability distribution
    """
    if k >= len(probs):
        return probs

    # Get indices sorted by probability (descending)
    indexed_probs = [(i, p) for i, p in enumerate(probs)]
    indexed_probs.sort(key=lambda x: x[1], reverse=True)

    # Keep only top-k
    top_k_indices = set(idx for idx, _ in indexed_probs[:k])

    # Zero out probabilities not in top-k
    new_probs = [p if i in top_k_indices else 0.0 for i, p in enumerate(probs)]

    # Renormalize
    total = sum(new_probs)
    if total > 0:
        new_probs = [p / total for p in new_probs]

    return new_probs


def nucleus_sampling(probs, p):
    """
    Apply nucleus (top-p) sampling to probability distribution.

    Args:
        probs: Probability distribution (list of floats)
        p: Cumulative probability threshold

    Returns:
        Modified probability distribution
    """
    if p >= 1.0:
        return probs

    # Get indices sorted by probability (descending)
    indexed_probs = [(i, prob) for i, prob in enumerate(probs)]
    indexed_probs.sort(key=lambda x: x[1], reverse=True)

    # Find nucleus (tokens whose cumulative probability <= p)
    cumsum = 0.0
    nucleus_indices = set()

    for idx, prob in indexed_probs:
        cumsum += prob
        nucleus_indices.add(idx)
        if cumsum >= p:
            break

    # Zero out probabilities not in nucleus
    new_probs = [prob if i in nucleus_indices else 0.0 for i, prob in enumerate(probs)]

    # Renormalize
    total = sum(new_probs)
    if total > 0:
        new_probs = [prob / total for prob in new_probs]

    return new_probs


def temperature_sampling(probs, temperature):
    """
    Apply temperature scaling to logits before softmax.
    Note: This should be applied to logits, not probabilities.

    Args:
        logits: Logit values (list of floats)
        temperature: Temperature parameter (higher = more random)

    Returns:
        Temperature-scaled probabilities
    """
    if temperature == 1.0:
        return softmax_row(logits)

    # Scale logits by temperature
    scaled_logits = [logit / temperature for logit in logits]

    # Apply softmax
    return softmax_row(scaled_logits)


def sample_categorical(probs):
    """
    Sample from categorical distribution.

    Args:
        probs: Probability distribution (list of floats)

    Returns:
        Sampled index
    """
    r = random.random()
    cumsum = 0.0

    for i, p in enumerate(probs):
        cumsum += p
        if r <= cumsum:
            return i

    # Fallback to last index
    return len(probs) - 1


def beam_search(model, prompt_ids, beam_size=5, max_len=50):
    """
    Beam search decoding (simplified implementation).

    Args:
        model: Generator model
        prompt_ids: Initial prompt token IDs
        beam_size: Number of beams to maintain
        max_len: Maximum sequence length

    Returns:
        List of (sequence, score) tuples
    """
    # Initialize beams with prompt
    beams = [(prompt_ids[:], 0.0)]  # (sequence, log_prob)
    eos_id = 3  # Assuming EOS token ID is 3

    for _ in range(max_len - len(prompt_ids)):
        new_beams = []

        for sequence, score in beams:
            # Skip if sequence already ended
            if sequence[-1] == eos_id:
                new_beams.append((sequence, score))
                continue

            # Get next token probabilities
            hidden_states = model.init_hidden()

            # Process sequence to get current state
            for token_id in sequence:
                _, hidden_states = model.forward_step(token_id, hidden_states)

            # Get logits for next token
            logits, _ = model.forward_step(sequence[-1], hidden_states)
            probs = softmax_row(logits)

            # Add top beam_size continuations
            indexed_probs = [(i, p) for i, p in enumerate(probs)]
            indexed_probs.sort(key=lambda x: x[1], reverse=True)

            for token_id, prob in indexed_probs[:beam_size]:
                new_sequence = sequence + [token_id]
                new_score = score + log(max(prob, 1e-10))
                new_beams.append((new_sequence, new_score))

        # Keep top beam_size beams
        new_beams.sort(key=lambda x: x[1], reverse=True)
        beams = new_beams[:beam_size]

    return beams


def structured_reasoning_sample(model, tokenizer, prompt_ids, max_len=50, temperature=1.0):
    """
    Simplified structured sampling that just uses regular sampling.

    Args:
        model: Generator model
        tokenizer: Tokenizer instance
        prompt_ids: Initial prompt token IDs
        max_len: Maximum sequence length
        temperature: Sampling temperature

    Returns:
        Generated sequence
    """
    try:
        # Use regular sampling for now to avoid complexity
        generated_ids, _ = model.sample(
            prompt_ids=prompt_ids,
            max_len=max_len,
            temperature=temperature
        )
        return generated_ids
    except Exception as e:
        print(f"Structured sampling failed: {e}")
        # Fallback to simple generation
        return prompt_ids + [tokenizer.get_token_id('<EOS>')]


def clean_reasoning_text(text, special_tokens):
    """
    Clean and format reasoning text for better readability.

    Args:
        text: Generated text
        special_tokens: Dictionary of special tokens

    Returns:
        Cleaned and formatted text
    """
    # Replace special tokens with formatted versions
    text = text.replace(special_tokens['REASON_START'], '\n[REASONING]')
    text = text.replace(special_tokens['REASON_END'], '\n[/REASONING]\n')
    text = text.replace(special_tokens['RESULT_START'], '[CONCLUSION]')
    text = text.replace(special_tokens['RESULT_END'], '\n[/CONCLUSION]')

    # Clean up extra spaces and newlines
    import re
    text = re.sub(r'\s+', ' ', text)
    text = re.sub(r'\n\s*\n', '\n', text)
    text = text.strip()

    return text
