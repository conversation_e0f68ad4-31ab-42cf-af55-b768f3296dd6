"""
OPTIMIZED DATA PREPARATION
Professional data processing for sequential training architecture.

This script properly separates and prepares data for:
1. Base language training (general text)
2. Reasoning fine-tuning (structured logical patterns)
3. Quality assessment (validation sets)

Usage:
  python prepare_data_optimized.py --full      # Prepare all data
  python prepare_data_optimized.py --base      # Prepare base language data only
  python prepare_data_optimized.py --reasoning # Prepare reasoning data only
"""
import sys
import os
import re
import json
import random
import argparse
from collections import Counter
from typing import List, Tuple, Dict


class OptimizedDataProcessor:
    """Professional data processor with proper separation of concerns."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.vocab = None
        
    def process_base_language_data(self) -> List[str]:
        """Process general language data for base model training."""
        print("📚 Processing Base Language Data")
        print("=" * 50)
        
        sentences = []
        
        if not os.path.exists("data/raw_text.txt"):
            print("❌ raw_text.txt not found")
            return sentences
        
        print("📖 Reading raw_text.txt...")
        with open("data/raw_text.txt", 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        
        print(f"📊 Found {len(lines)} raw lines")
        
        # Process and filter sentences
        for line in lines:
            line = line.strip()
            
            # Quality filters
            if (10 <= len(line) <= 200 and  # Reasonable length
                not line.startswith(('Given:', 'If ', 'Premise:', 'Therefore:')) and  # Not reasoning
                line.count(' ') >= 2 and  # Has multiple words
                any(c.isalpha() for c in line)):  # Contains letters
                
                # Clean the sentence
                cleaned = self._clean_sentence(line)
                if cleaned:
                    sentences.append(cleaned)
                    
                    if len(sentences) >= self.config['max_base_sentences']:
                        break
        
        print(f"✅ Processed {len(sentences)} base language sentences")
        
        # Save base language data
        os.makedirs("data/processed", exist_ok=True)
        with open("data/processed/base_language.txt", 'w', encoding='utf-8') as f:
            for sentence in sentences:
                f.write(sentence + '\n')
        
        print("💾 Saved: data/processed/base_language.txt")
        return sentences
    
    def process_reasoning_data(self) -> List[Tuple[str, str]]:
        """Process structured reasoning data for fine-tuning."""
        print("\n🧠 Processing Reasoning Data")
        print("=" * 50)
        
        reasoning_pairs = []
        
        if not os.path.exists("data/reasoning_raw.txt"):
            print("❌ reasoning_raw.txt not found")
            return reasoning_pairs
        
        print("📖 Reading reasoning_raw.txt...")
        with open("data/reasoning_raw.txt", 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Extract structured reasoning patterns
        patterns = [
            (r'Given: (.+?)\. Therefore: (.+?)\.', 'given_therefore'),
            (r'If (.+?)\., then (.+?)\.', 'if_then'),
            (r'If (.+?)\., then NOT \((.+?)\.\)', 'if_then_not'),
            (r'Premise: (.+?)\. Contradicts: (.+?)\.', 'premise_contradicts'),
            (r'Given: (.+?)\. Possibly: (.+?)\.', 'given_possibly'),
            (r"From '(.+?)' we cannot conclude '(.+?)'", 'cannot_conclude'),
        ]
        
        pattern_counts = {}
        
        for pattern, pattern_type in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            pattern_counts[pattern_type] = len(matches)
            
            for match in matches:
                premise, conclusion = match
                
                # Create clean reasoning pairs
                if pattern_type == 'given_therefore':
                    input_text = f"Given: {self._clean_reasoning_text(premise)}"
                    output_text = f"Therefore: {self._clean_reasoning_text(conclusion)}"
                elif pattern_type == 'if_then':
                    input_text = f"If {self._clean_reasoning_text(premise)}"
                    output_text = f"then {self._clean_reasoning_text(conclusion)}"
                elif pattern_type == 'if_then_not':
                    input_text = f"If {self._clean_reasoning_text(premise)}"
                    output_text = f"then NOT {self._clean_reasoning_text(conclusion)}"
                elif pattern_type == 'premise_contradicts':
                    input_text = f"Premise: {self._clean_reasoning_text(premise)}"
                    output_text = f"Contradicts: {self._clean_reasoning_text(conclusion)}"
                elif pattern_type == 'given_possibly':
                    input_text = f"Given: {self._clean_reasoning_text(premise)}"
                    output_text = f"Possibly: {self._clean_reasoning_text(conclusion)}"
                elif pattern_type == 'cannot_conclude':
                    input_text = f"From {self._clean_reasoning_text(premise)}"
                    output_text = f"we cannot conclude {self._clean_reasoning_text(conclusion)}"
                
                # Quality check
                if (len(input_text) > 10 and len(output_text) > 5 and
                    len(input_text) < 150 and len(output_text) < 150):
                    reasoning_pairs.append((input_text, output_text))
                
                if len(reasoning_pairs) >= self.config['max_reasoning_pairs']:
                    break
            
            if len(reasoning_pairs) >= self.config['max_reasoning_pairs']:
                break
        
        print("📊 Pattern Statistics:")
        for pattern_type, count in pattern_counts.items():
            print(f"  {pattern_type}: {count:,} matches")
        
        print(f"✅ Processed {len(reasoning_pairs)} reasoning pairs")
        
        # Save reasoning data
        with open("data/processed/reasoning_pairs.jsonl", 'w', encoding='utf-8') as f:
            for input_text, output_text in reasoning_pairs:
                pair = {"input": input_text, "output": output_text}
                f.write(json.dumps(pair, ensure_ascii=False) + '\n')
        
        print("💾 Saved: data/processed/reasoning_pairs.jsonl")
        return reasoning_pairs
    
    def build_vocabulary(self, base_sentences: List[str], reasoning_pairs: List[Tuple[str, str]]):
        """Build optimized vocabulary from all data."""
        print("\n📚 Building Optimized Vocabulary")
        print("=" * 50)
        
        # Collect all text
        all_text = []
        
        # Add base language text
        for sentence in base_sentences:
            all_text.extend(sentence.split())
        
        # Add reasoning text
        for input_text, output_text in reasoning_pairs:
            all_text.extend(input_text.split())
            all_text.extend(output_text.split())
        
        print(f"📊 Total tokens: {len(all_text):,}")
        
        # Count word frequencies
        word_counts = Counter(all_text)
        print(f"📊 Unique tokens: {len(word_counts):,}")
        
        # Create vocabulary with special tokens
        special_tokens = ['<PAD>', '<UNK>', '<BOS>', '<EOS>']
        
        # Add most frequent words
        vocab_words = special_tokens.copy()
        for word, count in word_counts.most_common(self.config['vocab_size'] - len(special_tokens)):
            if word not in vocab_words:  # Avoid duplicates
                vocab_words.append(word)
        
        # Create vocabulary mapping
        vocab_data = {
            'vocab': {word: i for i, word in enumerate(vocab_words)},
            'id_to_token': {str(i): word for i, word in enumerate(vocab_words)},
            'vocab_size': len(vocab_words),
            'special_tokens': {
                'PAD': 0,
                'UNK': 1,
                'BOS': 2,
                'EOS': 3
            }
        }
        
        # Save vocabulary
        os.makedirs("data", exist_ok=True)
        with open("data/vocab.txt", 'w', encoding='utf-8') as f:
            json.dump(vocab_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Built vocabulary: {len(vocab_words)} tokens")
        print("💾 Saved: data/vocab.txt")
        
        # Show vocabulary statistics
        print("\n📊 Vocabulary Statistics:")
        print(f"  Special tokens: {len(special_tokens)}")
        print(f"  Content tokens: {len(vocab_words) - len(special_tokens)}")
        print(f"  Coverage: {sum(word_counts[w] for w in vocab_words if w in word_counts) / len(all_text):.1%}")
        
        self.vocab = vocab_data
        return vocab_data
    
    def create_train_val_splits(self, base_sentences: List[str], reasoning_pairs: List[Tuple[str, str]]):
        """Create training and validation splits."""
        print("\n📊 Creating Train/Validation Splits")
        print("=" * 50)
        
        # Split base language data
        random.shuffle(base_sentences)
        base_split = int(len(base_sentences) * 0.9)
        base_train = base_sentences[:base_split]
        base_val = base_sentences[base_split:]
        
        # Split reasoning data
        random.shuffle(reasoning_pairs)
        reasoning_split = int(len(reasoning_pairs) * 0.9)
        reasoning_train = reasoning_pairs[:reasoning_split]
        reasoning_val = reasoning_pairs[reasoning_split:]
        
        print(f"📚 Base Language: {len(base_train)} train, {len(base_val)} val")
        print(f"🧠 Reasoning: {len(reasoning_train)} train, {len(reasoning_val)} val")
        
        # Save splits
        splits = {
            'base_train': base_train,
            'base_val': base_val,
            'reasoning_train': reasoning_train,
            'reasoning_val': reasoning_val
        }
        
        with open("data/processed/data_splits.json", 'w', encoding='utf-8') as f:
            json.dump(splits, f, indent=2, ensure_ascii=False)
        
        print("💾 Saved: data/processed/data_splits.json")
        return splits
    
    def _clean_sentence(self, sentence: str) -> str:
        """Clean a general language sentence."""
        # Remove extra whitespace
        sentence = re.sub(r'\s+', ' ', sentence.strip())
        
        # Remove problematic characters but keep basic punctuation
        sentence = re.sub(r'[^\w\s.,!?;:()\-\'\"]+', '', sentence)
        
        # Ensure proper capitalization
        if sentence and sentence[0].islower():
            sentence = sentence[0].upper() + sentence[1:]
        
        return sentence
    
    def _clean_reasoning_text(self, text: str) -> str:
        """Clean reasoning text while preserving logical structure."""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove problematic characters
        text = re.sub(r'[^\w\s.,!?;:()\-\'\"]+', '', text)
        
        # Remove common artifacts
        artifacts = ['undefined', 'null', '...']
        for artifact in artifacts:
            text = text.replace(artifact, '').strip()
        
        return text
    
    def process_all_data(self):
        """Process all data for sequential training."""
        print("🚀 OPTIMIZED DATA PREPARATION")
        print("=" * 80)
        print("Preparing data for sequential training architecture")
        print("=" * 80)
        
        # Process base language data
        base_sentences = self.process_base_language_data()
        
        # Process reasoning data
        reasoning_pairs = self.process_reasoning_data()
        
        # Build vocabulary
        vocab_data = self.build_vocabulary(base_sentences, reasoning_pairs)
        
        # Create train/val splits
        splits = self.create_train_val_splits(base_sentences, reasoning_pairs)
        
        print("\n" + "=" * 80)
        print("🎉 DATA PREPARATION COMPLETE!")
        print("=" * 80)
        print("📁 Created files:")
        print("  • data/vocab.txt (vocabulary)")
        print("  • data/processed/base_language.txt (general language)")
        print("  • data/processed/reasoning_pairs.jsonl (reasoning data)")
        print("  • data/processed/data_splits.json (train/val splits)")
        print("\n🚀 Ready for sequential training:")
        print("   python train_optimized.py --full --level balanced")
        print("=" * 80)


def get_config(level: str = "balanced") -> Dict:
    """Get data processing configuration."""
    configs = {
        "fast": {
            "max_base_sentences": 2000,
            "max_reasoning_pairs": 1000,
            "vocab_size": 2000
        },
        "balanced": {
            "max_base_sentences": 5000,
            "max_reasoning_pairs": 3000,
            "vocab_size": 3000
        },
        "quality": {
            "max_base_sentences": 10000,
            "max_reasoning_pairs": 5000,
            "vocab_size": 5000
        }
    }
    
    return configs.get(level, configs["balanced"])


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Optimized Data Preparation")
    parser.add_argument("--full", action="store_true", help="Process all data")
    parser.add_argument("--base", action="store_true", help="Process base language data only")
    parser.add_argument("--reasoning", action="store_true", help="Process reasoning data only")
    parser.add_argument("--level", choices=["fast", "balanced", "quality"], 
                       default="balanced", help="Processing level")
    
    args = parser.parse_args()
    
    # Get configuration
    config = get_config(args.level)
    
    # Initialize processor
    processor = OptimizedDataProcessor(config)
    
    try:
        if args.full:
            processor.process_all_data()
        elif args.base:
            processor.process_base_language_data()
        elif args.reasoning:
            processor.process_reasoning_data()
        else:
            print("❌ Please specify --full, --base, or --reasoning")
            print("Examples:")
            print("  python prepare_data_optimized.py --full --level balanced")
            print("  python prepare_data_optimized.py --base")
            
    except Exception as e:
        print(f"❌ Data preparation failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
