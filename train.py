"""
UNIFIED NLP GAN Reasoner Training
Single file with 5 training levels and ALL data sources.

Usage:
  python train_unified.py --level very_fast     # 30 seconds, basic learning
  python train_unified.py --level fast          # 2 minutes, good learning
  python train_unified.py --level balanced      # 5 minutes, solid learning
  python train_unified.py --level accurate      # 15 minutes, high quality
  python train_unified.py --level very_accurate # 45 minutes, best quality
"""
import sys
import os
import time
import random
import math
import json
import argparse
from collections import defaultdict, Counter

# Add current directory to path
sys.path.append('.')

# Import components
from models.generator import Generator
from nlp.tokenizer import Tokenizer
from nn.optim import Adam
from nn.layers import CrossEntropyLoss

# TRAINING CONFIGURATIONS
TRAINING_CONFIGS = {
    'very_fast': {
        'max_examples': 200,
        'vocab_size': 500,
        'embed_dim': 32,
        'hidden_dim': 64,
        'epochs': 3,
        'learning_rate': 0.01,
        'max_seq_len': 15,
        'method': 'simple_ngram',
        'description': '30 seconds - Quick test with n-gram model'
    },
    'fast': {
        'max_examples': 500,
        'vocab_size': 1000,
        'embed_dim': 32,
        'hidden_dim': 64,
        'epochs': 5,
        'learning_rate': 0.005,
        'max_seq_len': 20,
        'method': 'smart_neural',
        'description': '2 minutes - Smart neural updates'
    },
    'balanced': {
        'max_examples': 1000,
        'vocab_size': 2000,
        'embed_dim': 64,
        'hidden_dim': 128,
        'epochs': 8,
        'learning_rate': 0.003,
        'max_seq_len': 25,
        'method': 'smart_neural',
        'description': '5 minutes - Balanced performance'
    },
    'accurate': {
        'max_examples': 2000,
        'vocab_size': 3000,
        'embed_dim': 64,
        'hidden_dim': 128,
        'epochs': 12,
        'learning_rate': 0.002,
        'max_seq_len': 30,
        'method': 'finite_diff',
        'description': '15 minutes - Real gradients'
    },
    'very_accurate': {
        'max_examples': 5000,
        'vocab_size': 5000,
        'embed_dim': 128,
        'hidden_dim': 256,
        'epochs': 20,
        'learning_rate': 0.001,
        'max_seq_len': 40,
        'method': 'finite_diff',
        'description': '45 minutes - Best quality'
    }
}


def prepare_training_data(config):
    """
    Prepare training data from ALL sources: raw_text.txt AND reasoning_raw.txt
    Split examples evenly between both sources.
    """
    print("📚 STEP 1: Preparing training data from ALL sources...")

    all_examples = []
    max_per_source = config['max_examples'] // 2  # Split evenly

    # Process raw_text.txt (general language)
    if os.path.exists("data/raw_text.txt"):
        print(f"  📖 Processing data/raw_text.txt (target: {max_per_source} examples)...")
        with open("data/raw_text.txt", 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()

        text_examples = 0
        for line in lines:
            if text_examples >= max_per_source:
                break

            line = line.strip()
            if len(line) > 10 and len(line) < 150:
                words = line.split()
                if len(words) >= 4:
                    # Create input-target pairs for language modeling
                    for i in range(2, min(len(words), 8)):  # Limit to avoid too many from one line
                        if text_examples >= max_per_source:
                            break
                        input_text = ' '.join(words[:i])
                        target_text = words[i]
                        all_examples.append(f"{input_text}\t{target_text}")
                        text_examples += 1

        print(f"    ✅ Added {text_examples} examples from raw_text.txt")

    # Process reasoning_raw.txt (structured reasoning patterns)
    if os.path.exists("data/reasoning_raw.txt"):
        print(f"  📖 Processing data/reasoning_raw.txt (target: {max_per_source} examples)...")
        with open("data/reasoning_raw.txt", 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        reasoning_examples = 0

        # Parse structured reasoning patterns
        patterns = [
            # Given: X. Therefore: Y.
            (r'Given: (.+?)\. Therefore: (.+?)\.', 'given_therefore'),
            # If X., then Y.
            (r'If (.+?)\., then (.+?)\.', 'if_then'),
            # If X., then NOT (Y.)
            (r'If (.+?)\., then NOT \((.+?)\.\)', 'if_then_not'),
            # Premise: X. Contradicts: Y.
            (r'Premise: (.+?)\. Contradicts: (.+?)\.', 'premise_contradicts'),
            # Given: X. Possibly: Y.
            (r'Given: (.+?)\. Possibly: (.+?)\.', 'given_possibly'),
            # From 'X' we cannot conclude 'Y'
            (r"From '(.+?)' we cannot conclude '(.+?)'", 'cannot_conclude'),
        ]

        import re

        for pattern, pattern_type in patterns:
            if reasoning_examples >= max_per_source:
                break

            matches = re.findall(pattern, content, re.IGNORECASE)

            for match in matches:
                if reasoning_examples >= max_per_source:
                    break

                premise, conclusion = match

                # Create reasoning input-target pairs
                if pattern_type == 'given_therefore':
                    # "Given: X" -> "Therefore: Y"
                    input_text = f"Given: {premise}"
                    target_text = f"Therefore: {conclusion}"
                elif pattern_type == 'if_then':
                    # "If X" -> "then Y"
                    input_text = f"If {premise}"
                    target_text = f"then {conclusion}"
                elif pattern_type == 'if_then_not':
                    # "If X" -> "then NOT Y"
                    input_text = f"If {premise}"
                    target_text = f"then NOT {conclusion}"
                elif pattern_type == 'premise_contradicts':
                    # "Premise: X" -> "Contradicts: Y"
                    input_text = f"Premise: {premise}"
                    target_text = f"Contradicts: {conclusion}"
                elif pattern_type == 'given_possibly':
                    # "Given: X" -> "Possibly: Y"
                    input_text = f"Given: {premise}"
                    target_text = f"Possibly: {conclusion}"
                elif pattern_type == 'cannot_conclude':
                    # "From X" -> "we cannot conclude Y"
                    input_text = f"From {premise}"
                    target_text = f"we cannot conclude {conclusion}"

                # Add the reasoning pair
                all_examples.append(f"{input_text}\t{target_text}")
                reasoning_examples += 1

                # Also create word-level predictions within each part
                if reasoning_examples < max_per_source:
                    words = premise.split()
                    if len(words) >= 4:
                        for i in range(2, min(len(words), 6)):
                            if reasoning_examples >= max_per_source:
                                break
                            input_text = ' '.join(words[:i])
                            target_text = words[i]
                            all_examples.append(f"{input_text}\t{target_text}")
                            reasoning_examples += 1

        print(f"    ✅ Added {reasoning_examples} examples from reasoning_raw.txt")
        print(f"    🧠 Parsed structured reasoning patterns: Given/Therefore, If/Then, Premise/Contradicts")

    # Shuffle and split
    random.shuffle(all_examples)
    split_point = int(0.9 * len(all_examples))
    train_examples = all_examples[:split_point]
    val_examples = all_examples[split_point:]

    # Save processed data
    os.makedirs("data", exist_ok=True)

    with open("data/train_reasoning.txt", 'w', encoding='utf-8') as f:
        for example in train_examples:
            f.write(example + '\n')

    with open("data/val_reasoning.txt", 'w', encoding='utf-8') as f:
        for example in val_examples:
            f.write(example + '\n')

    print(f"✅ STEP 1 COMPLETE:")
    print(f"  📊 Total examples: {len(all_examples)}")
    print(f"  📈 Training: {len(train_examples)}")
    print(f"  📉 Validation: {len(val_examples)}")
    print(f"  💾 Saved: data/train_reasoning.txt, data/val_reasoning.txt")

    return train_examples, val_examples


def build_vocabulary(train_examples, val_examples, config):
    """Build vocabulary from training data."""
    print("\n📚 STEP 2: Building vocabulary...")

    all_text = []
    for example in train_examples + val_examples:
        input_text, target_text = example.split('\t')
        all_text.append(input_text)
        all_text.append(target_text)

    tokenizer = Tokenizer()
    tokenizer.build_vocab(all_text, min_freq=1, max_size=config['vocab_size'])
    tokenizer.save("data/vocab.txt")

    print(f"✅ STEP 2 COMPLETE:")
    print(f"  📊 Vocabulary size: {tokenizer.get_vocab_size()}")
    print(f"  💾 Saved: data/vocab.txt")

    return tokenizer


def prepare_sequences(examples, tokenizer, config):
    """Convert examples to token sequences."""
    print(f"\n🔄 STEP 3: Converting to sequences...")

    sequences = []
    for example in examples:
        try:
            input_text, target_text = example.split('\t')

            input_ids = tokenizer.encode(input_text, add_bos=True, add_eos=False, max_len=config['max_seq_len']//2)
            target_ids = tokenizer.encode(target_text, add_bos=False, add_eos=True, max_len=config['max_seq_len']//2)

            if input_ids and target_ids:
                full_sequence = input_ids + target_ids
                if len(full_sequence) >= 2:
                    sequences.append(full_sequence)

        except Exception as e:
            continue

    print(f"✅ STEP 3 COMPLETE:")
    print(f"  📦 Sequences created: {len(sequences)}")

    return sequences


class SimpleNGramModel:
    """Simple n-gram model for fast training."""

    def __init__(self, vocab_size, ngram_size=3):
        self.vocab_size = vocab_size
        self.ngram_size = ngram_size
        self.ngram_counts = defaultdict(Counter)
        self.context_counts = defaultdict(int)

    def train(self, sequences):
        """Train the n-gram model."""
        print(f"🧠 Training {self.ngram_size}-gram model...")

        total_ngrams = 0
        for seq in sequences:
            if len(seq) >= self.ngram_size:
                for i in range(len(seq) - self.ngram_size + 1):
                    context = tuple(seq[i:i + self.ngram_size - 1])
                    next_token = seq[i + self.ngram_size - 1]

                    self.ngram_counts[context][next_token] += 1
                    self.context_counts[context] += 1
                    total_ngrams += 1

        print(f"✅ Learned {total_ngrams} n-grams from {len(self.ngram_counts)} contexts")

    def predict_next(self, context):
        """Predict next token."""
        context = tuple(context[-(self.ngram_size-1):])

        if context in self.ngram_counts:
            candidates = self.ngram_counts[context]
            total_count = sum(candidates.values())

            rand_val = random.random() * total_count
            cumsum = 0
            for token, count in candidates.items():
                cumsum += count
                if rand_val <= cumsum:
                    return token

        return random.randint(0, self.vocab_size - 1)

    def generate_sequence(self, start_tokens, max_length=20):
        """Generate sequence."""
        sequence = list(start_tokens)

        for _ in range(max_length):
            next_token = self.predict_next(sequence)
            sequence.append(next_token)
            if next_token == 3:  # EOS
                break

        return sequence

    def compute_perplexity(self, test_sequences):
        """Compute perplexity."""
        total_log_prob = 0
        total_tokens = 0

        for seq in test_sequences:
            if len(seq) >= self.ngram_size:
                for i in range(len(seq) - self.ngram_size + 1):
                    context = tuple(seq[i:i + self.ngram_size - 1])
                    next_token = seq[i + self.ngram_size - 1]

                    if context in self.ngram_counts and next_token in self.ngram_counts[context]:
                        prob = self.ngram_counts[context][next_token] / self.context_counts[context]
                    else:
                        prob = 1.0 / self.vocab_size

                    total_log_prob += math.log(prob)
                    total_tokens += 1

        if total_tokens > 0:
            return math.exp(-total_log_prob / total_tokens)
        return float('inf')

    def save(self, filepath):
        """Save model."""
        model_data = {
            'vocab_size': self.vocab_size,
            'ngram_size': self.ngram_size,
            'ngram_counts': {','.join(map(str, k)): dict(v) for k, v in self.ngram_counts.items()},
            'context_counts': {','.join(map(str, k)): v for k, v in self.context_counts.items()}
        }

        with open(filepath, 'w') as f:
            json.dump(model_data, f)

    def load(self, filepath):
        """Load model from file."""
        with open(filepath, 'r') as f:
            model_data = json.load(f)

        self.vocab_size = model_data['vocab_size']
        self.ngram_size = model_data['ngram_size']
        self.ngram_counts = defaultdict(Counter)
        self.context_counts = defaultdict(int)

        for k, v in model_data['ngram_counts'].items():
            context = tuple(map(int, k.split(','))) if k else ()
            self.ngram_counts[context] = Counter({int(token): count for token, count in v.items()})

        for k, v in model_data['context_counts'].items():
            context = tuple(map(int, k.split(','))) if k else ()
            self.context_counts[context] = v


def train_simple_ngram(train_sequences, val_sequences, tokenizer, config):
    """Train simple n-gram model."""
    print(f"\n🎯 STEP 4: Training Simple N-Gram Model...")

    model = SimpleNGramModel(tokenizer.get_vocab_size(), ngram_size=3)
    model.train(train_sequences)

    # Evaluate
    train_perplexity = model.compute_perplexity(train_sequences[:50])
    val_perplexity = model.compute_perplexity(val_sequences)

    print(f"\n📊 EVALUATION:")
    print(f"  📈 Train Perplexity: {train_perplexity:.2f}")
    print(f"  📉 Val Perplexity: {val_perplexity:.2f}")

    # Save model
    os.makedirs("models_out", exist_ok=True)
    model.save("models_out/simple_model.json")

    # Test generation
    print(f"\n🧪 TESTING GENERATION:")
    test_context = tokenizer.encode("I am", add_bos=True, add_eos=False, max_len=5)
    generated = model.generate_sequence(test_context, max_length=10)
    generated_text = tokenizer.decode(generated)
    print(f"  Input: 'I am'")
    print(f"  Generated: '{generated_text}'")

    return model


def smart_parameter_update(generator, loss, prev_loss, learning_rate):
    """Smart parameter update using loss-based heuristics."""
    params = generator.get_all_params()

    # Adaptive learning based on loss change
    if prev_loss is not None:
        loss_change = loss - prev_loss
        if loss_change > 0:  # Loss increased
            lr_scale = 0.5  # Reduce learning rate
        else:  # Loss decreased
            lr_scale = 1.2  # Increase learning rate slightly
    else:
        lr_scale = 1.0

    effective_lr = learning_rate * lr_scale

    # Update parameters with smart heuristics
    for param in params:
        if isinstance(param[0], list):
            # 2D parameter (matrix)
            rows, cols = len(param), len(param[0])

            # Update a subset of parameters based on loss magnitude
            update_fraction = min(0.1, loss / 10.0)  # Update more when loss is high
            num_updates = max(1, int(rows * cols * update_fraction))

            for _ in range(num_updates):
                i = random.randint(0, rows - 1)
                j = random.randint(0, cols - 1)

                # Smart update direction based on loss
                if loss > 5.0:  # High loss - make bigger changes
                    update = effective_lr * random.uniform(-0.5, 0.5)
                elif loss > 3.0:  # Medium loss - moderate changes
                    update = effective_lr * random.uniform(-0.2, 0.2)
                else:  # Low loss - small changes
                    update = effective_lr * random.uniform(-0.1, 0.1)

                param[i][j] -= update
        else:
            # 1D parameter (vector)
            length = len(param)

            # Update a subset of parameters
            update_fraction = min(0.2, loss / 8.0)
            num_updates = max(1, int(length * update_fraction))

            for _ in range(num_updates):
                i = random.randint(0, length - 1)

                # Smart update direction based on loss
                if loss > 5.0:
                    update = effective_lr * random.uniform(-0.3, 0.3)
                elif loss > 3.0:
                    update = effective_lr * random.uniform(-0.15, 0.15)
                else:
                    update = effective_lr * random.uniform(-0.05, 0.05)

                param[i] -= update


def train_smart_neural(train_sequences, val_sequences, tokenizer, config):
    """Train neural model with smart parameter updates."""
    print(f"\n🎯 STEP 4: Training Smart Neural Model...")

    # Create generator
    generator = Generator(
        vocab_size=tokenizer.get_vocab_size(),
        embed_dim=config['embed_dim'],
        hidden_dim=config['hidden_dim'],
        num_layers=1
    )

    # Convert sequences to batches
    train_batches = []
    for seq in train_sequences:
        if len(seq) >= 2:
            train_batches.append((seq[:-1], seq[1:]))

    val_batches = []
    for seq in val_sequences:
        if len(seq) >= 2:
            val_batches.append((seq[:-1], seq[1:]))

    loss_fn = CrossEntropyLoss()
    best_loss = float('inf')
    prev_loss = None

    for epoch in range(config['epochs']):
        print(f"\n🔄 EPOCH {epoch + 1}/{config['epochs']}")
        epoch_start = time.time()

        # Training
        train_losses = []
        random.shuffle(train_batches)

        for batch_idx, (seq_input, seq_target) in enumerate(train_batches):
            try:
                # Forward pass
                logits, _ = generator.forward(seq_input, teacher_forcing=True)

                # Compute loss
                loss = loss_fn.forward(logits, seq_target)
                train_losses.append(loss)

                # SMART PARAMETER UPDATE
                smart_parameter_update(generator, loss, prev_loss, config['learning_rate'])
                prev_loss = loss

                # Progress update
                if batch_idx % 100 == 0:
                    avg_loss = sum(train_losses) / len(train_losses)
                    print(f"  ⚡ Batch {batch_idx}: Loss {loss:.4f}, Avg {avg_loss:.4f}")

            except Exception as e:
                continue

        # Validation
        val_losses = []
        for seq_input, seq_target in val_batches[:50]:
            try:
                logits, _ = generator.forward(seq_input, teacher_forcing=True)
                loss = loss_fn.forward(logits, seq_target)
                val_losses.append(loss)
            except:
                continue

        # Epoch summary
        train_loss = sum(train_losses) / max(len(train_losses), 1)
        val_loss = sum(val_losses) / max(len(val_losses), 1)
        epoch_time = time.time() - epoch_start

        print(f"✅ EPOCH {epoch + 1} COMPLETE:")
        print(f"  📈 Train Loss: {train_loss:.4f}")
        print(f"  📉 Val Loss: {val_loss:.4f}")
        print(f"  ⏱️ Time: {epoch_time:.1f}s")

        # Save best model
        if val_loss < best_loss:
            best_loss = val_loss
            os.makedirs("models_out", exist_ok=True)
            generator.save("models_out/generator_final.json")
            print(f"  💾 New best model saved!")

    return generator


def train_finite_diff(train_sequences, val_sequences, tokenizer, config):
    """Train neural model with finite difference gradients."""
    print(f"\n🎯 STEP 4: Training with Finite Difference Gradients...")
    print("⚠️  This method is slow but mathematically correct!")

    # Create generator
    generator = Generator(
        vocab_size=tokenizer.get_vocab_size(),
        embed_dim=config['embed_dim'],
        hidden_dim=config['hidden_dim'],
        num_layers=1
    )

    # Convert sequences to batches
    train_batches = []
    for seq in train_sequences:
        if len(seq) >= 2:
            train_batches.append((seq[:-1], seq[1:]))

    val_batches = []
    for seq in val_sequences:
        if len(seq) >= 2:
            val_batches.append((seq[:-1], seq[1:]))

    optimizer = Adam(generator.get_all_params(), lr=config['learning_rate'])
    loss_fn = CrossEntropyLoss()
    best_loss = float('inf')

    for epoch in range(config['epochs']):
        print(f"\n🔄 EPOCH {epoch + 1}/{config['epochs']}")
        epoch_start = time.time()

        # Training
        train_losses = []
        random.shuffle(train_batches)

        for batch_idx, (seq_input, seq_target) in enumerate(train_batches):
            try:
                # Forward pass
                logits, _ = generator.forward(seq_input, teacher_forcing=True)

                # Compute loss
                loss = loss_fn.forward(logits, seq_target)
                train_losses.append(loss)

                # FINITE DIFFERENCE GRADIENTS (simplified for speed)
                if batch_idx % 5 == 0:  # Only compute gradients every 5 batches
                    params = generator.get_all_params()
                    grads = []
                    epsilon = 1e-4

                    for param in params:
                        if isinstance(param[0], list):
                            # 2D parameter - sample a few positions
                            grad = [[0.0 for _ in range(len(param[0]))] for _ in range(len(param))]

                            # Sample 2 positions for speed
                            for _ in range(2):
                                i = random.randint(0, len(param) - 1)
                                j = random.randint(0, len(param[0]) - 1)

                                original_val = param[i][j]

                                # Positive perturbation
                                param[i][j] = original_val + epsilon
                                try:
                                    logits_plus, _ = generator.forward(seq_input, teacher_forcing=True)
                                    loss_plus = loss_fn.forward(logits_plus, seq_target)
                                except:
                                    loss_plus = loss

                                # Negative perturbation
                                param[i][j] = original_val - epsilon
                                try:
                                    logits_minus, _ = generator.forward(seq_input, teacher_forcing=True)
                                    loss_minus = loss_fn.forward(logits_minus, seq_target)
                                except:
                                    loss_minus = loss

                                # Compute gradient
                                grad[i][j] = (loss_plus - loss_minus) / (2 * epsilon)

                                # Restore original value
                                param[i][j] = original_val

                            grads.append(grad)
                        else:
                            # 1D parameter - sample a few positions
                            grad = [0.0 for _ in range(len(param))]

                            # Sample 3 positions for speed
                            for _ in range(3):
                                i = random.randint(0, len(param) - 1)

                                original_val = param[i]

                                # Positive perturbation
                                param[i] = original_val + epsilon
                                try:
                                    logits_plus, _ = generator.forward(seq_input, teacher_forcing=True)
                                    loss_plus = loss_fn.forward(logits_plus, seq_target)
                                except:
                                    loss_plus = loss

                                # Negative perturbation
                                param[i] = original_val - epsilon
                                try:
                                    logits_minus, _ = generator.forward(seq_input, teacher_forcing=True)
                                    loss_minus = loss_fn.forward(logits_minus, seq_target)
                                except:
                                    loss_minus = loss

                                # Compute gradient
                                grad[i] = (loss_plus - loss_minus) / (2 * epsilon)

                                # Restore original value
                                param[i] = original_val

                            grads.append(grad)

                    # Apply gradients
                    optimizer.step(grads)

                # Progress update
                if batch_idx % 50 == 0:
                    avg_loss = sum(train_losses) / len(train_losses)
                    print(f"  ⚡ Batch {batch_idx}: Loss {loss:.4f}, Avg {avg_loss:.4f}")

            except Exception as e:
                continue

        # Validation
        val_losses = []
        for seq_input, seq_target in val_batches[:20]:
            try:
                logits, _ = generator.forward(seq_input, teacher_forcing=True)
                loss = loss_fn.forward(logits, seq_target)
                val_losses.append(loss)
            except:
                continue

        # Epoch summary
        train_loss = sum(train_losses) / max(len(train_losses), 1)
        val_loss = sum(val_losses) / max(len(val_losses), 1)
        epoch_time = time.time() - epoch_start

        print(f"✅ EPOCH {epoch + 1} COMPLETE:")
        print(f"  📈 Train Loss: {train_loss:.4f}")
        print(f"  📉 Val Loss: {val_loss:.4f}")
        print(f"  ⏱️ Time: {epoch_time:.1f}s")

        # Save best model
        if val_loss < best_loss:
            best_loss = val_loss
            os.makedirs("models_out", exist_ok=True)
            generator.save("models_out/generator_final.json")
            print(f"  💾 New best model saved!")

    return generator


def main():
    """Main training function with level selection."""
    parser = argparse.ArgumentParser(description="Unified NLP GAN Reasoner Training")
    parser.add_argument("--level", type=str, default="very_fast",
                       choices=['very_fast', 'fast', 'balanced', 'accurate', 'very_accurate'],
                       help="Training level (very_fast, fast, balanced, accurate, very_accurate)")

    args = parser.parse_args()

    if args.level not in TRAINING_CONFIGS:
        print(f"❌ Invalid level: {args.level}")
        print("Available levels:", list(TRAINING_CONFIGS.keys()))
        return

    config = TRAINING_CONFIGS[args.level]

    print(f"🚀 UNIFIED NLP GAN REASONER TRAINING")
    print("=" * 60)
    print(f"📋 Level: {args.level.upper()}")
    print(f"📊 Config: {config['description']}")
    print(f"🔧 Method: {config['method']}")
    print(f"📈 Examples: {config['max_examples']} | Vocab: {config['vocab_size']} | Epochs: {config['epochs']}")
    print("=" * 60)

    start_time = time.time()

    try:
        # Execute all steps
        train_examples, val_examples = prepare_training_data(config)
        tokenizer = build_vocabulary(train_examples, val_examples, config)
        train_sequences = prepare_sequences(train_examples, tokenizer, config)
        val_sequences = prepare_sequences(val_examples, tokenizer, config)

        # Train based on method
        if config['method'] == 'simple_ngram':
            model = train_simple_ngram(train_sequences, val_sequences, tokenizer, config)
            model_type = "Simple N-Gram"
        elif config['method'] == 'smart_neural':
            model = train_smart_neural(train_sequences, val_sequences, tokenizer, config)
            model_type = "Smart Neural"
        elif config['method'] == 'finite_diff':
            model = train_finite_diff(train_sequences, val_sequences, tokenizer, config)
            model_type = "Finite Difference Neural"
        else:
            raise ValueError(f"Unknown method: {config['method']}")

        # Final summary
        total_time = time.time() - start_time
        print("\n" + "=" * 60)
        print("🎉 TRAINING COMPLETED!")
        print(f"📋 Level: {args.level.upper()}")
        print(f"🧠 Model Type: {model_type}")
        print(f"⏱️ Total Time: {total_time:.1f}s ({total_time/60:.1f} minutes)")
        print(f"📊 Data: {len(train_examples)} train, {len(val_examples)} val")
        print(f"📚 Vocab: {tokenizer.get_vocab_size()} tokens")

        if config['method'] == 'simple_ngram':
            print(f"💾 Model saved: models_out/simple_model.json")
            print("\n🚀 Ready to chat: python chat_simple.py")
        else:
            print(f"💾 Model saved: models_out/generator_final.json")
            print("\n🚀 Ready to chat: python chat.py")

        print("=" * 60)

        # Show next level suggestions
        levels = list(TRAINING_CONFIGS.keys())
        current_idx = levels.index(args.level)
        if current_idx < len(levels) - 1:
            next_level = levels[current_idx + 1]
            next_config = TRAINING_CONFIGS[next_level]
            print(f"\n💡 Try next level: python train_unified.py --level {next_level}")
            print(f"   {next_config['description']}")

    except Exception as e:
        print(f"\n❌ Training failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()