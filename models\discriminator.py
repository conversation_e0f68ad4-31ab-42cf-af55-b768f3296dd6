"""
Discriminator model for sequence classification.
Classifies sequences as real or fake, with optional reasoning scoring.
"""
import sys
import os
import math
import json

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from nn.layers import *
from utils.math_ops import *
import config


class Discriminator:
    """Discriminator for real/fake sequence classification."""

    def __init__(self, vocab_size, embed_dim=None, hidden_dim=None, use_reasoning_head=False):
        """
        Initialize discriminator.

        Args:
            vocab_size: Size of vocabulary
            embed_dim: Embedding dimension
            hidden_dim: Hidden dimension for encoder
            use_reasoning_head: Whether to include reasoning quality scoring
        """
        self.vocab_size = vocab_size
        self.embed_dim = embed_dim or config.EMBED_DIM
        self.hidden_dim = hidden_dim or config.HIDDEN_DIM
        self.use_reasoning_head = use_reasoning_head

        # Initialize layers
        self.embedding = Embedding(vocab_size, self.embed_dim)

        # Simple encoder (single GRU layer)
        self.encoder = GRUCell(self.embed_dim, self.hidden_dim)

        # Classification head (real/fake)
        self.classifier = Dense(self.hidden_dim, 1)  # Single output for binary classification

        # Optional reasoning quality head
        if use_reasoning_head:
            self.reasoning_head = Dense(self.hidden_dim, 1)  # Reasoning quality score
        else:
            self.reasoning_head = None

        # Cache for training
        self.cache = {}

    def sigmoid(self, x):
        """Sigmoid activation function."""
        if isinstance(x, list):
            return [1.0 / (1.0 + exp(-val)) for val in x]
        else:
            return 1.0 / (1.0 + exp(-x))

    def forward(self, input_ids, mask=None):
        """
        Forward pass.

        Args:
            input_ids: Input token IDs (seq_len,) or (batch_size, seq_len)
            mask: Optional mask for padding (same shape as input_ids)

        Returns:
            prob: Real/fake probability (scalar or batch)
            reason_score: Optional reasoning quality score (if use_reasoning_head=True)
        """
        # Handle single sequence
        if isinstance(input_ids[0], int):
            input_ids = [input_ids]
            if mask is not None:
                mask = [mask]
            single_sequence = True
        else:
            single_sequence = False

        batch_size = len(input_ids)

        batch_probs = []
        batch_reason_scores = []

        for b in range(batch_size):
            seq_ids = input_ids[b]
            seq_mask = mask[b] if mask else None

            # Encode sequence
            hidden = zeros(self.hidden_dim)
            valid_steps = 0

            for t, token_id in enumerate(seq_ids):
                # Skip if masked
                if seq_mask and seq_mask[t] == 0:
                    continue

                # Embedding
                x = self.embedding.forward(token_id)

                # Encoder step
                hidden = self.encoder.forward(x, hidden)
                valid_steps += 1

            # If no valid steps, use zero hidden state
            if valid_steps == 0:
                hidden = zeros(self.hidden_dim)

            # Classification
            logit = self.classifier.forward(hidden)
            prob = self.sigmoid(logit[0])  # Convert to probability
            batch_probs.append(prob)

            # Optional reasoning scoring
            if self.use_reasoning_head:
                reason_logit = self.reasoning_head.forward(hidden)
                reason_score = self.sigmoid(reason_logit[0])  # Score between 0 and 1
                batch_reason_scores.append(reason_score)

        # Cache for backward pass
        self.cache = {
            'input_ids': input_ids,
            'mask': mask,
            'single_sequence': single_sequence,
            'batch_probs': batch_probs,
            'batch_reason_scores': batch_reason_scores
        }

        # Return results
        if single_sequence:
            prob = batch_probs[0]
            if self.use_reasoning_head:
                return prob, batch_reason_scores[0]
            else:
                return prob, None
        else:
            if self.use_reasoning_head:
                return batch_probs, batch_reason_scores
            else:
                return batch_probs, None

    def compute_bce_loss(self, probs, labels):
        """
        Compute binary cross-entropy loss.

        Args:
            probs: Predicted probabilities (scalar or list)
            labels: True labels (0 or 1, scalar or list)

        Returns:
            loss: Scalar loss value
        """
        if isinstance(probs, list):
            # Batch
            total_loss = 0.0
            for prob, label in zip(probs, labels):
                # Binary cross-entropy: -[y*log(p) + (1-y)*log(1-p)]
                prob = max(min(prob, 1 - 1e-7), 1e-7)  # Clip for numerical stability
                if label == 1:
                    total_loss += -log(prob)
                else:
                    total_loss += -log(1 - prob)

            return total_loss / len(probs)
        else:
            # Single example
            prob = max(min(probs, 1 - 1e-7), 1e-7)  # Clip for numerical stability
            if labels == 1:
                return -log(prob)
            else:
                return -log(1 - prob)

    def get_all_params(self):
        """Get all model parameters."""
        params = []

        # Embedding parameters
        params.extend(self.embedding.get_params())

        # Encoder parameters
        params.extend(self.encoder.get_params())

        # Classifier parameters
        params.extend(self.classifier.get_params())

        # Optional reasoning head parameters
        if self.use_reasoning_head:
            params.extend(self.reasoning_head.get_params())

        return params

    def get_all_grads(self):
        """Get all model gradients."""
        grads = []

        # Embedding gradients
        grads.extend(self.embedding.get_grads())

        # Encoder gradients
        grads.extend(self.encoder.get_grads())

        # Classifier gradients
        grads.extend(self.classifier.get_grads())

        # Optional reasoning head gradients
        if self.use_reasoning_head:
            grads.extend(self.reasoning_head.get_grads())

        return grads

    def save(self, path):
        """Save model parameters."""
        model_data = {
            'vocab_size': self.vocab_size,
            'embed_dim': self.embed_dim,
            'hidden_dim': self.hidden_dim,
            'use_reasoning_head': self.use_reasoning_head,
            'embedding_W': self.embedding.W,
            'encoder_params': {
                'Wz': self.encoder.Wz, 'Uz': self.encoder.Uz, 'bz': self.encoder.bz,
                'Wr': self.encoder.Wr, 'Ur': self.encoder.Ur, 'br': self.encoder.br,
                'Wh': self.encoder.Wh, 'Uh': self.encoder.Uh, 'bh': self.encoder.bh
            },
            'classifier_W': self.classifier.W,
            'classifier_b': self.classifier.b
        }

        if self.use_reasoning_head:
            model_data['reasoning_W'] = self.reasoning_head.W
            model_data['reasoning_b'] = self.reasoning_head.b

        with open(path, 'w') as f:
            json.dump(model_data, f, indent=2)

        print(f"Saved discriminator model to {path}")

    def load(self, path):
        """Load model parameters."""
        with open(path, 'r') as f:
            model_data = json.load(f)

        # Restore architecture
        self.vocab_size = model_data['vocab_size']
        self.embed_dim = model_data['embed_dim']
        self.hidden_dim = model_data['hidden_dim']
        self.use_reasoning_head = model_data['use_reasoning_head']

        # Restore embedding
        self.embedding.W = model_data['embedding_W']

        # Restore encoder
        encoder_params = model_data['encoder_params']
        self.encoder.Wz = encoder_params['Wz']
        self.encoder.Uz = encoder_params['Uz']
        self.encoder.bz = encoder_params['bz']
        self.encoder.Wr = encoder_params['Wr']
        self.encoder.Ur = encoder_params['Ur']
        self.encoder.br = encoder_params['br']
        self.encoder.Wh = encoder_params['Wh']
        self.encoder.Uh = encoder_params['Uh']
        self.encoder.bh = encoder_params['bh']

        # Restore classifier
        self.classifier.W = model_data['classifier_W']
        self.classifier.b = model_data['classifier_b']

        # Restore reasoning head if present
        if self.use_reasoning_head and 'reasoning_W' in model_data:
            self.reasoning_head.W = model_data['reasoning_W']
            self.reasoning_head.b = model_data['reasoning_b']

        print(f"Loaded discriminator model from {path}")


def test_discriminator():
    """Simple test for discriminator functionality."""
    print("Testing discriminator...")

    # Create small discriminator
    vocab_size = 20
    discriminator = Discriminator(vocab_size, embed_dim=16, hidden_dim=32, use_reasoning_head=True)

    # Test single sequence
    input_ids = [1, 5, 10, 3]  # Simple sequence
    prob, reason_score = discriminator.forward(input_ids)

    print(f"Single sequence probability: {prob:.4f}")
    print(f"Reasoning score: {reason_score:.4f}")

    # Test batch
    batch_ids = [[1, 5, 10, 3], [2, 7, 8, 3]]
    batch_probs, batch_reason_scores = discriminator.forward(batch_ids)

    print(f"Batch probabilities: {[f'{p:.4f}' for p in batch_probs]}")
    print(f"Batch reasoning scores: {[f'{s:.4f}' for s in batch_reason_scores]}")

    # Test loss computation
    labels = [1, 0]  # Real, fake
    loss = discriminator.compute_bce_loss(batch_probs, labels)
    print(f"BCE loss: {loss:.4f}")

    print("✓ Discriminator test passed")


if __name__ == "__main__":
    test_discriminator()
