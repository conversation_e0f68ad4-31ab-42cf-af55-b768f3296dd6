"""
Mathematical operations for neural networks.
All operations implemented without external dependencies for educational purposes.
"""
import math
import random


def zeros(shape):
    """Create a matrix/vector of zeros with given shape."""
    if isinstance(shape, int):
        return [0.0] * shape
    elif len(shape) == 1:
        return [0.0] * shape[0]
    elif len(shape) == 2:
        rows, cols = shape
        return [[0.0] * cols for _ in range(rows)]
    else:
        raise ValueError("Only 1D and 2D shapes supported")


def ones(shape):
    """Create a matrix/vector of ones with given shape."""
    if isinstance(shape, int):
        return [1.0] * shape
    elif len(shape) == 1:
        return [1.0] * shape[0]
    elif len(shape) == 2:
        rows, cols = shape
        return [[1.0] * cols for _ in range(rows)]
    else:
        raise ValueError("Only 1D and 2D shapes supported")


def random_uniform(shape, a=0.0, b=1.0):
    """Create random matrix/vector with uniform distribution in [a, b]."""
    if isinstance(shape, int):
        return [random.uniform(a, b) for _ in range(shape)]
    elif len(shape) == 1:
        return [random.uniform(a, b) for _ in range(shape[0])]
    elif len(shape) == 2:
        rows, cols = shape
        return [[random.uniform(a, b) for _ in range(cols)] for _ in range(rows)]
    else:
        raise ValueError("Only 1D and 2D shapes supported")


def random_gauss(shape, mean=0.0, std=1.0):
    """Create random matrix/vector with Gaussian distribution."""
    if isinstance(shape, int):
        return [random.gauss(mean, std) for _ in range(shape)]
    elif len(shape) == 1:
        return [random.gauss(mean, std) for _ in range(shape[0])]
    elif len(shape) == 2:
        rows, cols = shape
        return [[random.gauss(mean, std) for _ in range(cols)] for _ in range(rows)]
    else:
        raise ValueError("Only 1D and 2D shapes supported")


def get_shape(matrix):
    """Get shape of matrix/vector."""
    if isinstance(matrix[0], list):
        return [len(matrix), len(matrix[0])]
    else:
        return [len(matrix)]


def matmul(A, B):
    """Matrix multiplication: A (n x m) @ B (m x p) -> (n x p)."""
    if not isinstance(A[0], list) or not isinstance(B[0], list):
        raise ValueError("Both inputs must be 2D matrices")

    n, m = len(A), len(A[0])
    m2, p = len(B), len(B[0])

    if m != m2:
        raise ValueError(f"Matrix dimensions incompatible: {n}x{m} @ {m2}x{p}")

    # Initialize result matrix
    C = zeros((n, p))

    # Perform multiplication
    for i in range(n):
        for j in range(p):
            for k in range(m):
                C[i][j] += A[i][k] * B[k][j]

    return C


def matvec(A, v):
    """Matrix-vector multiplication: A (n x m) @ v (m,) -> (n,)."""
    if not isinstance(A[0], list):
        raise ValueError("A must be a 2D matrix")
    if isinstance(v[0], list):
        raise ValueError("v must be a 1D vector")

    n, m = len(A), len(A[0])
    if len(v) != m:
        raise ValueError(f"Dimension mismatch: {n}x{m} @ {len(v)}")

    result = zeros(n)
    for i in range(n):
        for j in range(m):
            result[i] += A[i][j] * v[j]

    return result


def transpose(A):
    """Transpose a matrix."""
    if not isinstance(A[0], list):
        raise ValueError("Input must be a 2D matrix")

    rows, cols = len(A), len(A[0])
    AT = zeros((cols, rows))

    for i in range(rows):
        for j in range(cols):
            AT[j][i] = A[i][j]

    return AT


def add(A, B):
    """Element-wise addition of matrices/vectors."""
    if isinstance(A[0], list) and isinstance(B[0], list):
        # Matrix addition
        if len(A) != len(B) or len(A[0]) != len(B[0]):
            raise ValueError("Matrix dimensions must match")

        rows, cols = len(A), len(A[0])
        C = zeros((rows, cols))
        for i in range(rows):
            for j in range(cols):
                C[i][j] = A[i][j] + B[i][j]
        return C
    elif not isinstance(A[0], list) and not isinstance(B[0], list):
        # Vector addition
        if len(A) != len(B):
            raise ValueError("Vector dimensions must match")

        return [A[i] + B[i] for i in range(len(A))]
    else:
        raise ValueError("Both inputs must be same type (matrix or vector)")


def sub(A, B):
    """Element-wise subtraction of matrices/vectors."""
    if isinstance(A[0], list) and isinstance(B[0], list):
        # Matrix subtraction
        if len(A) != len(B) or len(A[0]) != len(B[0]):
            raise ValueError("Matrix dimensions must match")

        rows, cols = len(A), len(A[0])
        C = zeros((rows, cols))
        for i in range(rows):
            for j in range(cols):
                C[i][j] = A[i][j] - B[i][j]
        return C
    elif not isinstance(A[0], list) and not isinstance(B[0], list):
        # Vector subtraction
        if len(A) != len(B):
            raise ValueError("Vector dimensions must match")

        return [A[i] - B[i] for i in range(len(A))]
    else:
        raise ValueError("Both inputs must be same type (matrix or vector)")


def scalar_mul(A, s):
    """Scalar multiplication of matrix/vector."""
    if isinstance(A[0], list):
        # Matrix scalar multiplication
        rows, cols = len(A), len(A[0])
        C = zeros((rows, cols))
        for i in range(rows):
            for j in range(cols):
                C[i][j] = A[i][j] * s
        return C
    else:
        # Vector scalar multiplication
        return [A[i] * s for i in range(len(A))]


def elementwise_mul(A, B):
    """Element-wise multiplication of matrices/vectors."""
    if isinstance(A[0], list) and isinstance(B[0], list):
        # Matrix element-wise multiplication
        if len(A) != len(B) or len(A[0]) != len(B[0]):
            raise ValueError("Matrix dimensions must match")

        rows, cols = len(A), len(A[0])
        C = zeros((rows, cols))
        for i in range(rows):
            for j in range(cols):
                C[i][j] = A[i][j] * B[i][j]
        return C
    elif not isinstance(A[0], list) and not isinstance(B[0], list):
        # Vector element-wise multiplication
        if len(A) != len(B):
            raise ValueError("Vector dimensions must match")

        return [A[i] * B[i] for i in range(len(A))]
    else:
        raise ValueError("Both inputs must be same type (matrix or vector)")


def softmax_row(vec):
    """Numerically stable softmax for a single vector."""
    if isinstance(vec[0], list):
        raise ValueError("Input must be a 1D vector")

    # Numerical stability: subtract max
    max_val = max(vec)
    exps = [math.exp(x - max_val) for x in vec]
    sum_exps = sum(exps)

    return [exp_val / sum_exps for exp_val in exps]


def softmax_rows(matrix):
    """Apply softmax to each row of a matrix."""
    if not isinstance(matrix[0], list):
        raise ValueError("Input must be a 2D matrix")

    result = []
    for row in matrix:
        result.append(softmax_row(row))

    return result


def log(x):
    """Safe logarithm wrapper."""
    if x <= 0:
        return -float('inf')
    return math.log(x)


def exp(x):
    """Safe exponential wrapper."""
    try:
        return math.exp(x)
    except OverflowError:
        return float('inf')


def argmax(vec):
    """Return index of maximum element."""
    if isinstance(vec[0], list):
        raise ValueError("Input must be a 1D vector")

    max_idx = 0
    max_val = vec[0]
    for i in range(1, len(vec)):
        if vec[i] > max_val:
            max_val = vec[i]
            max_idx = i

    return max_idx


def sum_vec(vec):
    """Sum elements of a vector."""
    if isinstance(vec[0], list):
        raise ValueError("Input must be a 1D vector")

    return sum(vec)


def clip(x, low, high):
    """Clip value to range [low, high]."""
    return max(low, min(high, x))


def clip_vec(vec, low, high):
    """Clip all elements of vector to range [low, high]."""
    if isinstance(vec[0], list):
        raise ValueError("Input must be a 1D vector")

    return [clip(x, low, high) for x in vec]


def copy_matrix(A):
    """Deep copy a matrix/vector."""
    if isinstance(A[0], list):
        return [[A[i][j] for j in range(len(A[0]))] for i in range(len(A))]
    else:
        return [A[i] for i in range(len(A))]


def norm(vec):
    """Compute L2 norm of a vector."""
    if isinstance(vec[0], list):
        raise ValueError("Input must be a 1D vector")

    return math.sqrt(sum(x * x for x in vec))


def normalize_vec(vec):
    """Normalize vector to unit length."""
    n = norm(vec)
    if n == 0:
        return vec[:]
    return [x / n for x in vec]
