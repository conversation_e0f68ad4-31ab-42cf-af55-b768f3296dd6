"""
Evaluation metrics for NLP GAN Reasoner.
Implements perplexity, classification accuracy, and text quality metrics.
"""
import sys
import os
import math
import re
from collections import Counter

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.math_ops import *


def perplexity(loss, num_tokens):
    """
    Compute perplexity from cross-entropy loss.

    Args:
        loss: Average cross-entropy loss
        num_tokens: Number of tokens

    Returns:
        Perplexity value
    """
    return exp(loss)


def classification_accuracy(predictions, targets):
    """
    Compute classification accuracy.

    Args:
        predictions: Predicted labels (list of ints or floats)
        targets: True labels (list of ints)

    Returns:
        Accuracy as float between 0 and 1
    """
    if len(predictions) != len(targets):
        raise ValueError("Predictions and targets must have same length")

    if not predictions:
        return 0.0

    correct = 0
    for pred, target in zip(predictions, targets):
        # Convert probability to binary prediction if needed
        if isinstance(pred, float):
            pred_label = 1 if pred > 0.5 else 0
        else:
            pred_label = pred

        if pred_label == target:
            correct += 1

    return correct / len(predictions)


def bleu_score_simple(candidate, reference, n=4):
    """
    Simple BLEU score implementation (approximation).

    Args:
        candidate: Generated text (string)
        reference: Reference text (string)
        n: Maximum n-gram order

    Returns:
        BLEU score approximation
    """
    # Tokenize
    candidate_tokens = candidate.lower().split()
    reference_tokens = reference.lower().split()

    if not candidate_tokens:
        return 0.0

    # Compute n-gram precisions
    precisions = []

    for i in range(1, n + 1):
        candidate_ngrams = get_ngrams(candidate_tokens, i)
        reference_ngrams = get_ngrams(reference_tokens, i)

        if not candidate_ngrams:
            precisions.append(0.0)
            continue

        # Count matches
        matches = 0
        for ngram in candidate_ngrams:
            if ngram in reference_ngrams:
                matches += min(candidate_ngrams[ngram], reference_ngrams[ngram])

        precision = matches / sum(candidate_ngrams.values())
        precisions.append(precision)

    # Geometric mean of precisions
    if any(p == 0 for p in precisions):
        return 0.0

    geo_mean = 1.0
    for p in precisions:
        geo_mean *= p
    geo_mean = geo_mean ** (1.0 / len(precisions))

    # Brevity penalty
    candidate_len = len(candidate_tokens)
    reference_len = len(reference_tokens)

    if candidate_len >= reference_len:
        bp = 1.0
    else:
        bp = exp(1 - reference_len / candidate_len)

    return bp * geo_mean


def get_ngrams(tokens, n):
    """Get n-grams from token list."""
    ngrams = Counter()
    for i in range(len(tokens) - n + 1):
        ngram = tuple(tokens[i:i + n])
        ngrams[ngram] += 1
    return ngrams


def rouge_l_simple(candidate, reference):
    """
    Simple ROUGE-L score implementation.

    Args:
        candidate: Generated text (string)
        reference: Reference text (string)

    Returns:
        ROUGE-L F1 score
    """
    candidate_tokens = candidate.lower().split()
    reference_tokens = reference.lower().split()

    if not candidate_tokens or not reference_tokens:
        return 0.0

    # Find longest common subsequence
    lcs_length = longest_common_subsequence(candidate_tokens, reference_tokens)

    if lcs_length == 0:
        return 0.0

    # Compute precision and recall
    precision = lcs_length / len(candidate_tokens)
    recall = lcs_length / len(reference_tokens)

    # F1 score
    if precision + recall == 0:
        return 0.0

    f1 = 2 * precision * recall / (precision + recall)
    return f1


def longest_common_subsequence(seq1, seq2):
    """Compute length of longest common subsequence."""
    m, n = len(seq1), len(seq2)

    # Create DP table
    dp = [[0] * (n + 1) for _ in range(m + 1)]

    # Fill DP table
    for i in range(1, m + 1):
        for j in range(1, n + 1):
            if seq1[i - 1] == seq2[j - 1]:
                dp[i][j] = dp[i - 1][j - 1] + 1
            else:
                dp[i][j] = max(dp[i - 1][j], dp[i][j - 1])

    return dp[m][n]


def reasoning_structure_score(text, special_tokens):
    """
    Score the reasoning structure of generated text.

    Args:
        text: Generated text (string)
        special_tokens: Dictionary of special tokens

    Returns:
        Structure score between 0 and 1
    """
    score = 0.0

    # Check for reasoning start/end tokens
    if special_tokens['REASON_START'] in text and special_tokens['REASON_END'] in text:
        score += 0.3

    # Check for result start/end tokens
    if special_tokens['RESULT_START'] in text and special_tokens['RESULT_END'] in text:
        score += 0.3

    # Check for logical connectors
    logical_words = ['because', 'since', 'therefore', 'thus', 'implies', 'contradicts']
    for word in logical_words:
        if word.lower() in text.lower():
            score += 0.1
            break

    # Check for reasoning keywords
    reasoning_words = ['entailment', 'contradiction', 'analysis', 'conclusion']
    for word in reasoning_words:
        if word.lower() in text.lower():
            score += 0.1
            break

    # Check for proper structure order
    reason_start_pos = text.find(special_tokens['REASON_START'])
    reason_end_pos = text.find(special_tokens['REASON_END'])
    result_start_pos = text.find(special_tokens['RESULT_START'])
    result_end_pos = text.find(special_tokens['RESULT_END'])

    if (reason_start_pos < reason_end_pos < result_start_pos < result_end_pos and
        reason_start_pos >= 0):
        score += 0.2

    return min(score, 1.0)


def evaluate_generation_quality(generated_texts, reference_texts, special_tokens):
    """
    Comprehensive evaluation of generation quality.

    Args:
        generated_texts: List of generated texts
        reference_texts: List of reference texts
        special_tokens: Dictionary of special tokens

    Returns:
        Dictionary of evaluation metrics
    """
    if len(generated_texts) != len(reference_texts):
        raise ValueError("Generated and reference texts must have same length")

    metrics = {
        'bleu_scores': [],
        'rouge_l_scores': [],
        'structure_scores': [],
        'avg_length': 0.0,
        'avg_bleu': 0.0,
        'avg_rouge_l': 0.0,
        'avg_structure': 0.0
    }

    total_length = 0

    for gen_text, ref_text in zip(generated_texts, reference_texts):
        # BLEU score
        bleu = bleu_score_simple(gen_text, ref_text)
        metrics['bleu_scores'].append(bleu)

        # ROUGE-L score
        rouge_l = rouge_l_simple(gen_text, ref_text)
        metrics['rouge_l_scores'].append(rouge_l)

        # Structure score
        structure = reasoning_structure_score(gen_text, special_tokens)
        metrics['structure_scores'].append(structure)

        # Length
        total_length += len(gen_text.split())

    # Compute averages
    n = len(generated_texts)
    metrics['avg_length'] = total_length / n
    metrics['avg_bleu'] = sum(metrics['bleu_scores']) / n
    metrics['avg_rouge_l'] = sum(metrics['rouge_l_scores']) / n
    metrics['avg_structure'] = sum(metrics['structure_scores']) / n

    return metrics
