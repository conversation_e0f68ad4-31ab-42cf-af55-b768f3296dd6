"""
OPTIMIZED SEQUENTIAL REASONING TRAINER
Professional-grade implementation with proper separation of concerns.

Architecture:
1. Stage 1: Base Language Model (foundation)
2. Stage 2: Reasoning Fine-tuning (logical capabilities)
3. Stage 3: Quality Refinement (optional GAN polishing)

Usage:
  python train_optimized.py --stage base      # Train base language model
  python train_optimized.py --stage reasoning # Fine-tune for reasoning
  python train_optimized.py --stage refine    # GAN quality refinement
  python train_optimized.py --full            # Run all stages
"""
import sys
import os
import time
import json
import random
import argparse
from collections import defaultdict, Counter
from typing import List, Tuple, Dict, Optional

# Add current directory to path
sys.path.append('.')

from models.generator import Generator
from models.discriminator import Discriminator
from nlp.tokenizer import Tokenizer
from nn.optim import Adam
from nn.layers import CrossEntropyLoss


class DataProcessor:
    """Handles data loading and preprocessing for each training stage."""
    
    def __init__(self, tokenizer: Tokenizer, max_seq_len: int = 32):
        self.tokenizer = tokenizer
        self.max_seq_len = max_seq_len
    
    def load_base_language_data(self, max_examples: int = 5000) -> List[List[int]]:
        """Load and process general language data for base training."""
        print("📚 Loading base language data...")
        
        sequences = []
        if not os.path.exists("data/raw_text.txt"):
            print("❌ raw_text.txt not found")
            return sequences
        
        with open("data/raw_text.txt", 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
        
        for line in lines[:max_examples]:
            line = line.strip()
            if 10 < len(line) < 200:  # Filter reasonable length
                tokens = self.tokenizer.encode(line, add_bos=True, add_eos=True, max_len=self.max_seq_len)
                if tokens and len(tokens) >= 4:
                    sequences.append(tokens)
        
        print(f"✅ Loaded {len(sequences)} base language sequences")
        return sequences
    
    def load_reasoning_data(self, max_examples: int = 3000) -> List[Tuple[List[int], List[int]]]:
        """Load and process reasoning data for fine-tuning."""
        print("🧠 Loading reasoning data...")
        
        pairs = []
        if not os.path.exists("data/reasoning_raw.txt"):
            print("❌ reasoning_raw.txt not found")
            return pairs
        
        with open("data/reasoning_raw.txt", 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # Extract structured reasoning patterns
        import re
        patterns = [
            (r'Given: (.+?)\. Therefore: (.+?)\.', 'given_therefore'),
            (r'If (.+?)\., then (.+?)\.', 'if_then'),
            (r'Premise: (.+?)\. Contradicts: (.+?)\.', 'premise_contradicts'),
        ]
        
        for pattern, pattern_type in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            
            for match in matches:
                premise, conclusion = match
                
                # Create structured input-output pairs
                if pattern_type == 'given_therefore':
                    input_text = f"Given: {premise.strip()}"
                    output_text = f"Therefore: {conclusion.strip()}"
                elif pattern_type == 'if_then':
                    input_text = f"If {premise.strip()}"
                    output_text = f"then {conclusion.strip()}"
                elif pattern_type == 'premise_contradicts':
                    input_text = f"Premise: {premise.strip()}"
                    output_text = f"Contradicts: {conclusion.strip()}"
                
                # Tokenize
                input_tokens = self.tokenizer.encode(input_text, add_bos=True, add_eos=False, max_len=20)
                output_tokens = self.tokenizer.encode(output_text, add_bos=False, add_eos=True, max_len=20)
                
                if input_tokens and output_tokens:
                    pairs.append((input_tokens, output_tokens))
                
                if len(pairs) >= max_examples:
                    break
            
            if len(pairs) >= max_examples:
                break
        
        print(f"✅ Loaded {len(pairs)} reasoning pairs")
        return pairs


class BaseLanguageTrainer:
    """Trains the base language model on general text."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.tokenizer = None
        self.model = None
    
    def train(self, sequences: List[List[int]]) -> Generator:
        """Train base language model."""
        print("\n🧠 STAGE 1: Training Base Language Model")
        print("=" * 60)
        
        # Initialize model
        vocab_size = self.tokenizer.get_vocab_size()
        self.model = Generator(
            vocab_size=vocab_size,
            embed_dim=self.config['embed_dim'],
            hidden_dim=self.config['hidden_dim'],
            num_layers=self.config['num_layers']
        )
        
        # Training loop
        loss_fn = CrossEntropyLoss()
        
        for epoch in range(self.config['base_epochs']):
            total_loss = 0
            random.shuffle(sequences)
            
            for i, sequence in enumerate(sequences):
                if len(sequence) < 2:
                    continue
                
                # Create input-target pairs
                for j in range(1, len(sequence)):
                    input_seq = sequence[:j]
                    target_token = sequence[j]
                    
                    # Forward pass
                    logits, _ = self.model.forward(input_seq)
                    
                    # Compute loss
                    if logits:
                        loss = loss_fn.forward(logits[-1:], [target_token])
                        total_loss += loss
                        
                        # Simple gradient update (simplified)
                        self.model.backward(loss_fn.backward())
                
                if i % 500 == 0 and i > 0:
                    avg_loss = total_loss / max(i, 1)
                    print(f"  Epoch {epoch+1}, Batch {i}: Avg Loss {avg_loss:.4f}")
            
            epoch_loss = total_loss / max(len(sequences), 1)
            print(f"✅ Epoch {epoch+1} Complete: Loss {epoch_loss:.4f}")
        
        # Save base model
        os.makedirs("models_out", exist_ok=True)
        self.model.save("models_out/base_language_model.json")
        print("💾 Saved base language model")
        
        return self.model


class ReasoningTrainer:
    """Fine-tunes the base model for reasoning capabilities."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.tokenizer = None
        self.model = None
    
    def train(self, reasoning_pairs: List[Tuple[List[int], List[int]]]) -> Generator:
        """Fine-tune for reasoning."""
        print("\n🔗 STAGE 2: Fine-tuning for Reasoning")
        print("=" * 60)
        
        # Load base model
        if os.path.exists("models_out/base_language_model.json"):
            vocab_size = self.tokenizer.get_vocab_size()
            self.model = Generator(
                vocab_size=vocab_size,
                embed_dim=self.config['embed_dim'],
                hidden_dim=self.config['hidden_dim'],
                num_layers=self.config['num_layers']
            )
            self.model.load("models_out/base_language_model.json")
            print("✅ Loaded base language model")
        else:
            raise FileNotFoundError("Base model not found! Run stage 1 first.")
        
        # Fine-tuning with lower learning rate
        loss_fn = CrossEntropyLoss()
        
        for epoch in range(self.config['reasoning_epochs']):
            total_loss = 0
            random.shuffle(reasoning_pairs)
            
            for i, (input_tokens, output_tokens) in enumerate(reasoning_pairs):
                # Create full sequence for training
                full_sequence = input_tokens + output_tokens
                
                # Train on the reasoning continuation
                for j in range(len(input_tokens), len(full_sequence)):
                    context = full_sequence[:j]
                    target_token = full_sequence[j]
                    
                    # Forward pass
                    logits, _ = self.model.forward(context)
                    
                    # Compute loss
                    if logits:
                        loss = loss_fn.forward(logits[-1:], [target_token])
                        total_loss += loss
                        
                        # Gradient update with lower learning rate
                        self.model.backward(loss_fn.backward(), lr_scale=0.1)
                
                if i % 200 == 0 and i > 0:
                    avg_loss = total_loss / max(i, 1)
                    print(f"  Reasoning Epoch {epoch+1}, Batch {i}: Avg Loss {avg_loss:.4f}")
            
            epoch_loss = total_loss / max(len(reasoning_pairs), 1)
            print(f"✅ Reasoning Epoch {epoch+1} Complete: Loss {epoch_loss:.4f}")
        
        # Save reasoning model
        self.model.save("models_out/reasoning_model.json")
        print("💾 Saved reasoning model")
        
        return self.model


class QualityRefiner:
    """Optional GAN-based quality refinement."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.tokenizer = None
        self.generator = None
        self.discriminator = None
    
    def train(self, all_data: List[List[int]]) -> Generator:
        """Refine quality with adversarial training."""
        print("\n🎯 STAGE 3: Quality Refinement (Optional)")
        print("=" * 60)
        
        # Load reasoning model
        if os.path.exists("models_out/reasoning_model.json"):
            vocab_size = self.tokenizer.get_vocab_size()
            self.generator = Generator(
                vocab_size=vocab_size,
                embed_dim=self.config['embed_dim'],
                hidden_dim=self.config['hidden_dim'],
                num_layers=self.config['num_layers']
            )
            self.generator.load("models_out/reasoning_model.json")
            print("✅ Loaded reasoning model")
        else:
            raise FileNotFoundError("Reasoning model not found! Run stage 2 first.")
        
        # Initialize discriminator
        vocab_size = self.tokenizer.get_vocab_size()
        self.discriminator = Discriminator(
            vocab_size=vocab_size,
            embed_dim=self.config['embed_dim'],
            hidden_dim=self.config['hidden_dim']
        )
        
        # Simplified adversarial training
        print("🥊 Adversarial training...")
        
        for epoch in range(self.config['refine_epochs']):
            # Train discriminator on real vs generated
            d_loss = self._train_discriminator_step(all_data)
            
            # Train generator to fool discriminator
            g_loss = self._train_generator_step(all_data)
            
            print(f"  Refine Epoch {epoch+1}: D_Loss {d_loss:.4f}, G_Loss {g_loss:.4f}")
        
        # Save final model
        self.generator.save("models_out/final_model.json")
        self.discriminator.save("models_out/discriminator.json")
        print("💾 Saved refined models")
        
        return self.generator
    
    def _train_discriminator_step(self, data: List[List[int]]) -> float:
        """Train discriminator for one step."""
        # Simplified discriminator training
        return 0.5  # Placeholder
    
    def _train_generator_step(self, data: List[List[int]]) -> float:
        """Train generator for one step."""
        # Simplified generator training
        return 0.5  # Placeholder


class OptimizedSequentialTrainer:
    """Main trainer that orchestrates the sequential training process."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.tokenizer = self._initialize_tokenizer()
        
        # Initialize stage trainers
        self.base_trainer = BaseLanguageTrainer(config)
        self.base_trainer.tokenizer = self.tokenizer
        
        self.reasoning_trainer = ReasoningTrainer(config)
        self.reasoning_trainer.tokenizer = self.tokenizer
        
        self.quality_refiner = QualityRefiner(config)
        self.quality_refiner.tokenizer = self.tokenizer
        
        # Data processor
        self.data_processor = DataProcessor(self.tokenizer, config['max_seq_len'])
    
    def _initialize_tokenizer(self) -> Tokenizer:
        """Initialize or load tokenizer."""
        tokenizer = Tokenizer()
        
        if os.path.exists("data/vocab.txt"):
            tokenizer.load("data/vocab.txt")
            print(f"✅ Loaded existing vocabulary: {tokenizer.get_vocab_size()} tokens")
        else:
            print("❌ Vocabulary not found. Run data preparation first.")
            raise FileNotFoundError("Run: python scripts/prepare_data.py")
        
        return tokenizer
    
    def train_stage_base(self):
        """Train base language model."""
        sequences = self.data_processor.load_base_language_data(self.config['max_base_examples'])
        model = self.base_trainer.train(sequences)
        return model
    
    def train_stage_reasoning(self):
        """Fine-tune for reasoning."""
        pairs = self.data_processor.load_reasoning_data(self.config['max_reasoning_examples'])
        model = self.reasoning_trainer.train(pairs)
        return model
    
    def train_stage_refine(self):
        """Optional quality refinement."""
        # Load combined data for refinement
        base_sequences = self.data_processor.load_base_language_data(1000)
        reasoning_pairs = self.data_processor.load_reasoning_data(1000)
        
        # Convert reasoning pairs to sequences
        reasoning_sequences = []
        for input_tokens, output_tokens in reasoning_pairs:
            reasoning_sequences.append(input_tokens + output_tokens)
        
        all_data = base_sequences + reasoning_sequences
        model = self.quality_refiner.train(all_data)
        return model
    
    def train_full_pipeline(self):
        """Run complete sequential training pipeline."""
        print("🚀 OPTIMIZED SEQUENTIAL TRAINING PIPELINE")
        print("=" * 80)
        
        start_time = time.time()
        
        # Stage 1: Base Language
        print("Starting Stage 1: Base Language Training...")
        self.train_stage_base()
        
        # Stage 2: Reasoning
        print("Starting Stage 2: Reasoning Fine-tuning...")
        self.train_stage_reasoning()
        
        # Stage 3: Quality Refinement (optional)
        if self.config.get('enable_refinement', False):
            print("Starting Stage 3: Quality Refinement...")
            self.train_stage_refine()
        
        total_time = time.time() - start_time
        
        print("\n" + "=" * 80)
        print("🎉 SEQUENTIAL TRAINING COMPLETE!")
        print(f"⏱️  Total Time: {total_time:.1f} seconds")
        print("📁 Models saved:")
        print("   • models_out/base_language_model.json")
        print("   • models_out/reasoning_model.json")
        if self.config.get('enable_refinement', False):
            print("   • models_out/final_model.json")
        print("\n🚀 Test with: python chat.py --model models_out/reasoning_model.json")
        print("=" * 80)


def get_config(level: str = "balanced") -> Dict:
    """Get training configuration."""
    configs = {
        "fast": {
            "max_base_examples": 1000,
            "max_reasoning_examples": 500,
            "embed_dim": 32,
            "hidden_dim": 64,
            "num_layers": 1,
            "base_epochs": 3,
            "reasoning_epochs": 2,
            "refine_epochs": 1,
            "max_seq_len": 24,
            "enable_refinement": False
        },
        "balanced": {
            "max_base_examples": 3000,
            "max_reasoning_examples": 1500,
            "embed_dim": 64,
            "hidden_dim": 128,
            "num_layers": 1,
            "base_epochs": 5,
            "reasoning_epochs": 3,
            "refine_epochs": 2,
            "max_seq_len": 32,
            "enable_refinement": False
        },
        "quality": {
            "max_base_examples": 5000,
            "max_reasoning_examples": 3000,
            "embed_dim": 96,
            "hidden_dim": 192,
            "num_layers": 2,
            "base_epochs": 8,
            "reasoning_epochs": 5,
            "refine_epochs": 3,
            "max_seq_len": 40,
            "enable_refinement": True
        }
    }
    
    return configs.get(level, configs["balanced"])


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Optimized Sequential Reasoning Trainer")
    parser.add_argument("--stage", choices=["base", "reasoning", "refine"], 
                       help="Train specific stage")
    parser.add_argument("--full", action="store_true", 
                       help="Run complete pipeline")
    parser.add_argument("--level", choices=["fast", "balanced", "quality"], 
                       default="balanced", help="Training level")
    
    args = parser.parse_args()
    
    # Get configuration
    config = get_config(args.level)
    
    # Initialize trainer
    trainer = OptimizedSequentialTrainer(config)
    
    try:
        if args.full:
            trainer.train_full_pipeline()
        elif args.stage == "base":
            trainer.train_stage_base()
        elif args.stage == "reasoning":
            trainer.train_stage_reasoning()
        elif args.stage == "refine":
            trainer.train_stage_refine()
        else:
            print("❌ Please specify --stage or --full")
            print("Examples:")
            print("  python train_optimized.py --full --level balanced")
            print("  python train_optimized.py --stage base")
            
    except Exception as e:
        print(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
