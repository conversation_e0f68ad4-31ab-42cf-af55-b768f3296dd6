"""
Tokenizer implementation for NLP GAN Reasoner.
Handles vocabulary building, tokenization, and encoding/decoding.
"""
import sys
import os
import re
import json
from collections import Counter

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import config


class Tokenizer:
    """Simple tokenizer for reasoning text."""

    def __init__(self, special_tokens=None):
        """Initialize tokenizer with special tokens."""
        self.special_tokens = special_tokens or config.SPECIAL_TOKENS
        self.vocab = {}
        self.id_to_token = {}
        self.vocab_size = 0

        # Initialize with special tokens
        for token in self.special_tokens.values():
            self._add_token(token)

    def _add_token(self, token):
        """Add a token to vocabulary if not present."""
        if token not in self.vocab:
            self.vocab[token] = self.vocab_size
            self.id_to_token[self.vocab_size] = token
            self.vocab_size += 1

    def tokenize(self, text):
        """
        Tokenize text into tokens.
        Split on whitespace and separate punctuation.
        """
        if not text:
            return []

        # Basic tokenization: split on whitespace
        tokens = text.split()

        # Separate punctuation from words
        final_tokens = []
        punctuation = set('.,;:()?"![]{}')

        for token in tokens:
            if not token:
                continue

            # Check if token is entirely punctuation
            if all(c in punctuation for c in token):
                final_tokens.append(token)
                continue

            # Split punctuation from words
            current_token = ""
            for char in token:
                if char in punctuation:
                    if current_token:
                        final_tokens.append(current_token)
                        current_token = ""
                    final_tokens.append(char)
                else:
                    current_token += char

            if current_token:
                final_tokens.append(current_token)

        return final_tokens

    def build_vocab(self, lines, min_freq=1, max_size=None):
        """
        Build vocabulary from list of text lines.

        Args:
            lines: List of text strings
            min_freq: Minimum frequency for token inclusion
            max_size: Maximum vocabulary size
        """
        print(f"Building vocabulary from {len(lines)} lines...")

        # Count token frequencies
        token_counts = Counter()

        for line in lines:
            tokens = self.tokenize(line)
            token_counts.update(tokens)

        print(f"Found {len(token_counts)} unique tokens")

        # Sort by frequency (descending)
        sorted_tokens = sorted(token_counts.items(), key=lambda x: x[1], reverse=True)

        # Add tokens to vocabulary (special tokens already added)
        added_count = 0
        for token, count in sorted_tokens:
            if count < min_freq:
                break

            if max_size and self.vocab_size >= max_size:
                break

            if token not in self.vocab:
                self._add_token(token)
                added_count += 1

        print(f"Added {added_count} tokens to vocabulary")
        print(f"Final vocabulary size: {self.vocab_size}")

        # Show some statistics
        print(f"Most frequent tokens: {sorted_tokens[:10]}")

    def encode(self, text, add_bos=True, add_eos=True, max_len=None):
        """
        Encode text to list of token IDs.

        Args:
            text: Input text string
            add_bos: Whether to add beginning-of-sequence token
            add_eos: Whether to add end-of-sequence token
            max_len: Maximum sequence length (truncate if longer)

        Returns:
            List of token IDs
        """
        tokens = self.tokenize(text)

        # Convert tokens to IDs
        ids = []
        unk_id = self.vocab.get(self.special_tokens['UNK'], 0)

        for token in tokens:
            token_id = self.vocab.get(token, unk_id)
            ids.append(token_id)

        # Add special tokens
        if add_bos:
            bos_id = self.vocab.get(self.special_tokens['BOS'], 0)
            ids.insert(0, bos_id)

        if add_eos:
            eos_id = self.vocab.get(self.special_tokens['EOS'], 0)
            ids.append(eos_id)

        # Truncate if necessary
        if max_len and len(ids) > max_len:
            ids = ids[:max_len]
            # Ensure EOS is at the end if we truncated
            if add_eos:
                eos_id = self.vocab.get(self.special_tokens['EOS'], 0)
                ids[-1] = eos_id

        return ids

    def decode(self, ids):
        """
        Decode list of token IDs back to text.

        Args:
            ids: List of token IDs

        Returns:
            Decoded text string
        """
        tokens = []

        for token_id in ids:
            if token_id in self.id_to_token:
                token = self.id_to_token[token_id]
                # Skip special tokens in output (except reasoning tokens)
                if token in [self.special_tokens['PAD'], self.special_tokens['BOS'], self.special_tokens['EOS']]:
                    continue
                tokens.append(token)

        # Join tokens with spaces
        text = ' '.join(tokens)

        # Post-process spacing around punctuation
        text = re.sub(r' ([.,;:!?])', r'\1', text)  # Remove space before punctuation
        text = re.sub(r'([(\[]) ', r'\1', text)     # Remove space after opening brackets
        text = re.sub(r' ([\])])', r'\1', text)     # Remove space before closing brackets

        return text

    def pad_sequence(self, ids, max_len, pad_value=None):
        """
        Pad sequence to fixed length.

        Args:
            ids: List of token IDs
            max_len: Target length
            pad_value: Padding token ID (uses PAD token if None)

        Returns:
            Padded sequence
        """
        if pad_value is None:
            pad_value = self.vocab.get(self.special_tokens['PAD'], 0)

        if len(ids) >= max_len:
            return ids[:max_len]
        else:
            return ids + [pad_value] * (max_len - len(ids))

    def save(self, path):
        """Save tokenizer vocabulary to file."""
        vocab_data = {
            'vocab': self.vocab,
            'id_to_token': self.id_to_token,
            'vocab_size': self.vocab_size,
            'special_tokens': self.special_tokens
        }

        with open(path, 'w', encoding='utf-8') as f:
            json.dump(vocab_data, f, indent=2, ensure_ascii=False)

        print(f"Saved vocabulary to {path}")

    def load(self, path):
        """Load tokenizer vocabulary from file."""
        with open(path, 'r', encoding='utf-8') as f:
            vocab_data = json.load(f)

        self.vocab = vocab_data['vocab']
        self.id_to_token = {int(k): v for k, v in vocab_data['id_to_token'].items()}
        self.vocab_size = vocab_data['vocab_size']
        self.special_tokens = vocab_data['special_tokens']

        print(f"Loaded vocabulary from {path} (size: {self.vocab_size})")

    def get_vocab_size(self):
        """Get vocabulary size."""
        return self.vocab_size

    def get_token_id(self, token):
        """Get ID for a token."""
        return self.vocab.get(token, self.vocab.get(self.special_tokens['UNK'], 0))

    def get_token(self, token_id):
        """Get token for an ID."""
        return self.id_to_token.get(token_id, self.special_tokens['UNK'])


def build_vocab_from_file(train_file, tokenizer=None, save_path=None):
    """
    Build vocabulary from training file.

    Args:
        train_file: Path to training data file
        tokenizer: Existing tokenizer (creates new if None)
        save_path: Path to save vocabulary

    Returns:
        Tokenizer with built vocabulary
    """
    if tokenizer is None:
        tokenizer = Tokenizer()

    # Read training data
    lines = []
    with open(train_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line:
                # Split on tab to get input and target
                parts = line.split('\t')
                lines.extend(parts)

    # Build vocabulary
    tokenizer.build_vocab(lines, min_freq=config.VOCAB_MIN_FREQ, max_size=config.MAX_VOCAB)

    # Save if path provided
    if save_path:
        tokenizer.save(save_path)

    return tokenizer


if __name__ == "__main__":
    # Test tokenizer
    print("Testing tokenizer...")

    # Build vocabulary from training data
    tokenizer = build_vocab_from_file(config.TRAIN_FILE, save_path=config.VOCAB_FILE)

    # Test encoding/decoding
    test_text = "Premise: A dog runs in a grassy field."
    print(f"\nOriginal: {test_text}")

    ids = tokenizer.encode(test_text)
    print(f"Encoded: {ids}")

    decoded = tokenizer.decode(ids)
    print(f"Decoded: {decoded}")

    print("\nTokenizer test completed!")
