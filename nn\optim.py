"""
Optimizers for neural network training.
Implements Adam and SGD optimizers.
"""
import sys
import os
import math

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.math_ops import *


class Adam:
    """Adam optimizer implementation."""

    def __init__(self, params, lr=0.001, beta1=0.9, beta2=0.999, eps=1e-8):
        """
        Initialize Adam optimizer.

        Args:
            params: List of parameter arrays (matrices/vectors)
            lr: Learning rate
            beta1: First moment decay rate
            beta2: Second moment decay rate
            eps: Small constant for numerical stability
        """
        self.params = params
        self.lr = lr
        self.beta1 = beta1
        self.beta2 = beta2
        self.eps = eps

        # Initialize moment estimates
        self.m = []  # First moment
        self.v = []  # Second moment

        for param in params:
            if isinstance(param[0], list):
                # Matrix
                self.m.append(zeros((len(param), len(param[0]))))
                self.v.append(zeros((len(param), len(param[0]))))
            else:
                # Vector
                self.v.append(zeros(len(param)))
                self.m.append(zeros(len(param)))

        self.t = 0  # Time step

    def step(self, grads):
        """
        Perform one optimization step.

        Args:
            grads: List of gradients (same structure as params)
        """
        self.t += 1

        # Bias correction factors
        bias_correction1 = 1 - self.beta1 ** self.t
        bias_correction2 = 1 - self.beta2 ** self.t

        for i, (param, grad) in enumerate(zip(self.params, grads)):
            if isinstance(param[0], list):
                # Matrix parameters
                rows, cols = len(param), len(param[0])

                for r in range(rows):
                    for c in range(cols):
                        # Update biased first moment estimate
                        self.m[i][r][c] = self.beta1 * self.m[i][r][c] + (1 - self.beta1) * grad[r][c]

                        # Update biased second moment estimate
                        self.v[i][r][c] = self.beta2 * self.v[i][r][c] + (1 - self.beta2) * grad[r][c] * grad[r][c]

                        # Compute bias-corrected moment estimates
                        m_hat = self.m[i][r][c] / bias_correction1
                        v_hat = self.v[i][r][c] / bias_correction2

                        # Update parameters
                        param[r][c] -= self.lr * m_hat / (math.sqrt(v_hat) + self.eps)

            else:
                # Vector parameters
                for j in range(len(param)):
                    # Update biased first moment estimate
                    self.m[i][j] = self.beta1 * self.m[i][j] + (1 - self.beta1) * grad[j]

                    # Update biased second moment estimate
                    self.v[i][j] = self.beta2 * self.v[i][j] + (1 - self.beta2) * grad[j] * grad[j]

                    # Compute bias-corrected moment estimates
                    m_hat = self.m[i][j] / bias_correction1
                    v_hat = self.v[i][j] / bias_correction2

                    # Update parameters
                    param[j] -= self.lr * m_hat / (math.sqrt(v_hat) + self.eps)


class SGD:
    """Stochastic Gradient Descent optimizer."""

    def __init__(self, params, lr=0.01, momentum=0.0):
        """
        Initialize SGD optimizer.

        Args:
            params: List of parameter arrays
            lr: Learning rate
            momentum: Momentum factor
        """
        self.params = params
        self.lr = lr
        self.momentum = momentum

        # Initialize momentum buffers
        self.velocity = []

        for param in params:
            if isinstance(param[0], list):
                # Matrix
                self.velocity.append(zeros((len(param), len(param[0]))))
            else:
                # Vector
                self.velocity.append(zeros(len(param)))

    def step(self, grads):
        """
        Perform one optimization step.

        Args:
            grads: List of gradients (same structure as params)
        """
        for i, (param, grad) in enumerate(zip(self.params, grads)):
            if isinstance(param[0], list):
                # Matrix parameters
                rows, cols = len(param), len(param[0])

                for r in range(rows):
                    for c in range(cols):
                        # Update velocity
                        self.velocity[i][r][c] = self.momentum * self.velocity[i][r][c] + self.lr * grad[r][c]

                        # Update parameters
                        param[r][c] -= self.velocity[i][r][c]

            else:
                # Vector parameters
                for j in range(len(param)):
                    # Update velocity
                    self.velocity[i][j] = self.momentum * self.velocity[i][j] + self.lr * grad[j]

                    # Update parameters
                    param[j] -= self.velocity[i][j]


def clip_gradients_global_norm(grads, max_norm):
    """
    Clip gradients by global norm.

    Args:
        grads: List of gradient arrays
        max_norm: Maximum gradient norm

    Returns:
        Clipped gradients
    """
    # Compute global norm
    total_norm_sq = 0.0

    for grad in grads:
        if isinstance(grad[0], list):
            # Matrix
            for row in grad:
                for val in row:
                    total_norm_sq += val * val
        else:
            # Vector
            for val in grad:
                total_norm_sq += val * val

    total_norm = math.sqrt(total_norm_sq)

    # Clip if necessary
    if total_norm > max_norm:
        clip_factor = max_norm / total_norm

        clipped_grads = []
        for grad in grads:
            if isinstance(grad[0], list):
                # Matrix
                clipped_grad = []
                for row in grad:
                    clipped_row = [val * clip_factor for val in row]
                    clipped_grad.append(clipped_row)
                clipped_grads.append(clipped_grad)
            else:
                # Vector
                clipped_grad = [val * clip_factor for val in grad]
                clipped_grads.append(clipped_grad)

        return clipped_grads

    return grads
