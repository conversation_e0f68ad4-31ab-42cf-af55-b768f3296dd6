"""
Unit tests for neural network layers with gradient checking.
"""
import sys
import os
import math
import random

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from nn.layers import *
from utils.math_ops import *


def numerical_gradient(f, x, h=1e-5):
    """
    Compute numerical gradient using finite differences.

    Args:
        f: Function that takes x and returns scalar
        x: Input (vector or matrix)
        h: Step size

    Returns:
        Numerical gradient (same shape as x)
    """
    if isinstance(x[0], list):
        # Matrix
        grad = zeros((len(x), len(x[0])))
        for i in range(len(x)):
            for j in range(len(x[0])):
                # f(x + h)
                x[i][j] += h
                f_plus = f(x)

                # f(x - h)
                x[i][j] -= 2 * h
                f_minus = f(x)

                # Restore original value
                x[i][j] += h

                # Numerical gradient
                grad[i][j] = (f_plus - f_minus) / (2 * h)

        return grad
    else:
        # Vector
        grad = zeros(len(x))
        for i in range(len(x)):
            # f(x + h)
            x[i] += h
            f_plus = f(x)

            # f(x - h)
            x[i] -= 2 * h
            f_minus = f(x)

            # Restore original value
            x[i] += h

            # Numerical gradient
            grad[i] = (f_plus - f_minus) / (2 * h)

        return grad


def check_gradient(analytical_grad, numerical_grad, tolerance=1e-5):
    """
    Check if analytical and numerical gradients match.

    Args:
        analytical_grad: Gradient from backprop
        numerical_grad: Gradient from finite differences
        tolerance: Tolerance for comparison

    Returns:
        bool: True if gradients match within tolerance
    """
    if isinstance(analytical_grad[0], list):
        # Matrix
        for i in range(len(analytical_grad)):
            for j in range(len(analytical_grad[0])):
                diff = abs(analytical_grad[i][j] - numerical_grad[i][j])
                if diff > tolerance:
                    print(f"Gradient mismatch at [{i}][{j}]: analytical={analytical_grad[i][j]:.6f}, numerical={numerical_grad[i][j]:.6f}, diff={diff:.6f}")
                    return False
    else:
        # Vector
        for i in range(len(analytical_grad)):
            diff = abs(analytical_grad[i] - numerical_grad[i])
            if diff > tolerance:
                print(f"Gradient mismatch at [{i}]: analytical={analytical_grad[i]:.6f}, numerical={numerical_grad[i]:.6f}, diff={diff:.6f}")
                return False

    return True


def test_dense_gradient():
    """Test Dense layer gradient computation."""
    print("Testing Dense layer gradients...")

    # Create small dense layer
    layer = Dense(3, 2)

    # Test input
    x = [1.0, 2.0, 3.0]

    # Forward pass
    y = layer.forward(x)

    # Create dummy loss: sum of squares
    loss = sum(val * val for val in y)

    # Analytical gradient
    dout = [2 * val for val in y]  # Gradient of sum of squares
    dx = layer.backward(dout)

    # Numerical gradient for weights
    def loss_fn_W(W):
        layer.W = W
        y = layer.forward(x)
        return sum(val * val for val in y)

    numerical_dW = numerical_gradient(loss_fn_W, copy_matrix(layer.W))

    # Check weight gradients
    if check_gradient(layer.dW, numerical_dW):
        print("✓ Dense weight gradients match")
    else:
        print("✗ Dense weight gradients don't match")
        return False

    # Numerical gradient for bias
    def loss_fn_b(b):
        layer.b = b
        y = layer.forward(x)
        return sum(val * val for val in y)

    numerical_db = numerical_gradient(loss_fn_b, layer.b[:])

    # Check bias gradients
    if check_gradient(layer.db, numerical_db):
        print("✓ Dense bias gradients match")
    else:
        print("✗ Dense bias gradients don't match")
        return False

    return True


def test_embedding_gradient():
    """Test Embedding layer gradient computation."""
    print("Testing Embedding layer gradients...")

    # Create small embedding layer
    vocab_size = 5
    embed_dim = 3
    layer = Embedding(vocab_size, embed_dim)

    # Test input
    ids = [1, 2, 0]  # Sequence of token IDs

    # Forward pass
    embeddings = layer.forward(ids)

    # Create dummy loss: sum of squares of all embeddings
    loss = 0.0
    for emb in embeddings:
        for val in emb:
            loss += val * val

    # Analytical gradient
    dout = []
    for emb in embeddings:
        dout.append([2 * val for val in emb])  # Gradient of sum of squares

    layer.backward(dout)

    # Numerical gradient for embedding weights
    def loss_fn_W(W):
        layer.W = W
        embeddings = layer.forward(ids)
        loss = 0.0
        for emb in embeddings:
            for val in emb:
                loss += val * val
        return loss

    numerical_dW = numerical_gradient(loss_fn_W, copy_matrix(layer.W))

    # Check embedding gradients
    if check_gradient(layer.dW, numerical_dW):
        print("✓ Embedding gradients match")
        return True
    else:
        print("✗ Embedding gradients don't match")
        return False


def test_gru_gradient():
    """Test GRU cell gradient computation (simplified)."""
    print("Testing GRU cell gradients...")

    # Create small GRU cell
    input_dim = 2
    hidden_dim = 3
    cell = GRUCell(input_dim, hidden_dim)

    # Test inputs
    x = [1.0, 0.5]
    h_prev = [0.1, 0.2, 0.3]

    # Forward pass
    h = cell.forward(x, h_prev)

    # Create dummy loss: sum of squares
    loss = sum(val * val for val in h)

    # Analytical gradient
    dh = [2 * val for val in h]  # Gradient of sum of squares
    dx, dh_prev = cell.backward(dh)

    # Test one weight matrix (Wz) for numerical gradient
    def loss_fn_Wz(Wz):
        cell.Wz = Wz
        h = cell.forward(x, h_prev)
        return sum(val * val for val in h)

    numerical_dWz = numerical_gradient(loss_fn_Wz, copy_matrix(cell.Wz))

    # Check gradients (with relaxed tolerance due to complexity)
    if check_gradient(cell.dWz, numerical_dWz, tolerance=1e-4):
        print("✓ GRU Wz gradients match")
        return True
    else:
        print("✗ GRU Wz gradients don't match")
        return False


def test_cross_entropy_loss():
    """Test CrossEntropyLoss computation."""
    print("Testing CrossEntropyLoss...")

    # Create loss function
    loss_fn = CrossEntropyLoss()

    # Test data
    logits = [[1.0, 2.0, 0.5], [0.8, 1.5, 2.2]]  # 2 time steps, 3 classes
    targets = [1, 2]  # Target classes

    # Forward pass
    loss = loss_fn.forward(logits, targets)

    # Backward pass
    dlogits = loss_fn.backward()

    # Check that gradients sum to 0 for each time step (property of softmax)
    for t in range(len(dlogits)):
        grad_sum = sum(dlogits[t])
        if abs(grad_sum) > 1e-6:
            print(f"✗ CrossEntropy gradients don't sum to 0 at time {t}: {grad_sum}")
            return False

    print("✓ CrossEntropyLoss working correctly")
    return True


def run_all_tests():
    """Run all layer tests."""
    print("Running neural network layer tests...")

    # Set random seed for reproducibility
    random.seed(42)

    tests = [
        test_dense_gradient,
        test_embedding_gradient,
        test_gru_gradient,
        test_cross_entropy_loss
    ]

    passed = 0
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"Test {test.__name__} failed!")
        except Exception as e:
            print(f"Test {test.__name__} crashed: {e}")

    print(f"\n{passed}/{len(tests)} tests passed")

    if passed == len(tests):
        print("✅ All layer tests passed!")
        return True
    else:
        print("❌ Some layer tests failed!")
        return False


if __name__ == "__main__":
    run_all_tests()
