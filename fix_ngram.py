"""
Quick Fix for N-gram Model
Improves the current N-gram model to generate more coherent text.

The issue: N-gram model trained on mixed data produces incoherent output
The fix: Better probability smoothing and context handling
"""
import sys
import os
import json
import random
from collections import defaultdict, Counter

# Add current directory to path
sys.path.append('.')

from train import SimpleNGramModel
from nlp.tokenizer import Tokenizer


def fix_ngram_generation():
    """Fix the N-gram model generation to be more coherent."""
    print("🔧 FIXING N-GRAM MODEL")
    print("=" * 50)
    
    # Load existing model and tokenizer
    tokenizer = Tokenizer()
    tokenizer.load("data/vocab.txt")
    
    model = SimpleNGramModel(tokenizer.get_vocab_size())
    model.load("models_out/simple_model.json")
    
    print("✅ Loaded existing model")
    
    # Test current generation
    print("\n❌ BEFORE FIX:")
    test_input = "Given: A person is walking"
    input_tokens = tokenizer.encode(test_input, add_bos=True, add_eos=False, max_len=10)
    
    if input_tokens:
        old_output = model.generate_sequence(input_tokens, max_length=15)
        old_continuation = old_output[len(input_tokens):]
        old_text = tokenizer.decode(old_continuation)
        print(f"Input: {test_input}")
        print(f"Output: {old_text}")
    
    # Apply fixes
    print("\n🔧 APPLYING FIXES:")
    print("1. Better probability smoothing")
    print("2. Improved context handling")
    print("3. Reasoning-aware generation")
    
    # Create improved model
    improved_model = ImprovedNGramModel(tokenizer.get_vocab_size())
    
    # Copy data from old model
    improved_model.ngram_counts = model.ngram_counts
    improved_model.context_counts = model.context_counts
    improved_model.tokenizer = tokenizer
    
    # Test improved generation
    print("\n✅ AFTER FIX:")
    if input_tokens:
        new_output = improved_model.generate_sequence(input_tokens, max_length=15)
        new_continuation = new_output[len(input_tokens):]
        new_text = tokenizer.decode(new_continuation)
        print(f"Input: {test_input}")
        print(f"Output: {new_text}")
    
    # Save improved model
    improved_model.save("models_out/simple_model_fixed.json")
    print("\n💾 Saved improved model: models_out/simple_model_fixed.json")
    
    return improved_model


class ImprovedNGramModel(SimpleNGramModel):
    """Improved N-gram model with better generation."""
    
    def __init__(self, vocab_size, ngram_size=3):
        super().__init__(vocab_size, ngram_size)
        self.tokenizer = None
        
        # Special tokens
        self.PAD_TOKEN = 0
        self.UNK_TOKEN = 1
        self.BOS_TOKEN = 2
        self.EOS_TOKEN = 3
        
    def predict_next(self, context):
        """Improved next token prediction with smoothing."""
        context = tuple(context[-(self.ngram_size-1):])
        
        # Try exact context first
        if context in self.ngram_counts:
            candidates = self.ngram_counts[context]
            
            # Filter out padding tokens for better quality
            filtered_candidates = {
                token: count for token, count in candidates.items() 
                if token != self.PAD_TOKEN
            }
            
            if filtered_candidates:
                candidates = filtered_candidates
            
            # Apply smoothing
            total_count = sum(candidates.values())
            smoothing_factor = 0.1
            
            # Create probability distribution
            probs = []
            tokens = []
            
            for token, count in candidates.items():
                prob = (count + smoothing_factor) / (total_count + smoothing_factor * len(candidates))
                probs.append(prob)
                tokens.append(token)
            
            # Sample from distribution
            rand_val = random.random()
            cumsum = 0
            for i, prob in enumerate(probs):
                cumsum += prob
                if rand_val <= cumsum:
                    return tokens[i]
        
        # Fallback: try shorter context
        if len(context) > 1:
            shorter_context = context[1:]
            return self.predict_next(list(shorter_context))
        
        # Final fallback: avoid special tokens
        while True:
            token = random.randint(4, self.vocab_size - 1)  # Skip special tokens
            if token < self.vocab_size:
                return token
    
    def generate_sequence(self, start_tokens, max_length=20):
        """Improved sequence generation."""
        sequence = list(start_tokens)
        
        # Detect reasoning context
        if self.tokenizer:
            start_text = self.tokenizer.decode(start_tokens).lower()
            is_reasoning = any(keyword in start_text for keyword in 
                             ['given:', 'if ', 'premise:', 'therefore'])
        else:
            is_reasoning = False
        
        for i in range(max_length):
            next_token = self.predict_next(sequence)
            
            # Stop on EOS or if we have enough tokens
            if next_token == self.EOS_TOKEN:
                break
                
            sequence.append(next_token)
            
            # For reasoning, try to generate more coherent continuations
            if is_reasoning and i > 5:  # After a few tokens
                if self.tokenizer:
                    current_text = self.tokenizer.decode(sequence[len(start_tokens):])
                    # Stop if we have a reasonable reasoning continuation
                    if any(word in current_text.lower() for word in 
                          ['therefore', 'then', 'thus', 'so', 'because']):
                        break
        
        return sequence
    
    def save(self, filepath):
        """Save improved model."""
        model_data = {
            'vocab_size': self.vocab_size,
            'ngram_size': self.ngram_size,
            'ngram_counts': {','.join(map(str, k)): dict(v) for k, v in self.ngram_counts.items()},
            'context_counts': {','.join(map(str, k)): v for k, v in self.context_counts.items()},
            'model_type': 'improved_ngram'
        }
        
        with open(filepath, 'w') as f:
            json.dump(model_data, f)


def test_improvements():
    """Test the improvements with various inputs."""
    print("\n🧪 TESTING IMPROVEMENTS")
    print("=" * 50)
    
    # Load tokenizer
    tokenizer = Tokenizer()
    tokenizer.load("data/vocab.txt")
    
    # Load improved model
    model = ImprovedNGramModel(tokenizer.get_vocab_size())
    model.load("models_out/simple_model_fixed.json")
    model.tokenizer = tokenizer
    
    test_cases = [
        "Given: A person is walking",
        "If it rains",
        "Hello world",
        "The cat is",
        "Premise: All birds"
    ]
    
    for test_input in test_cases:
        input_tokens = tokenizer.encode(test_input, add_bos=True, add_eos=False, max_len=10)
        
        if input_tokens:
            output = model.generate_sequence(input_tokens, max_length=12)
            continuation = output[len(input_tokens):]
            continuation_text = tokenizer.decode(continuation)
            
            print(f"Input: '{test_input}'")
            print(f"Output: '{continuation_text}'")
            print()


def main():
    """Main function."""
    print("🔧 N-GRAM MODEL IMPROVEMENT")
    print("=" * 60)
    print("Fixing the current N-gram model for better coherence")
    print("=" * 60)
    
    # Apply fixes
    improved_model = fix_ngram_generation()
    
    # Test improvements
    test_improvements()
    
    print("=" * 60)
    print("✅ N-gram model improvements complete!")
    print("🚀 Test with: python chat.py --model models_out/simple_model_fixed.json")
    print("=" * 60)


if __name__ == "__main__":
    main()
