"""
Configuration file for NLP GAN Reasoner.
Central hyperparameters and special tokens.
"""

# Vocabulary settings
VOCAB_MIN_FREQ = 1
MAX_VOCAB = 10000

# Model architecture (optimized for better performance)
EMBED_DIM = 128
HIDDEN_DIM = 256
NUM_LAYERS = 2
MAX_SEQ_LEN = 150

# Training settings (optimized)
BATCH_SIZE = 4  # Smaller batch for stability
LEARNING_RATE_GEN = 5e-4  # Lower learning rate for stability
LEARNING_RATE_DIS = 1e-4
WARMUP_STEPS = 100  # Learning rate warmup

# GAN training (optimized)
ROLLOUTS = 4  # Fewer rollouts for faster training
MLE_PRETRAIN_EPOCHS = 50  # More pretraining for better foundation
D_PRETRAIN_EPOCHS = 10
ADV_STEPS = 800  # Fewer adversarial steps

# Training stability
GRADIENT_CLIP_NORM = 1.0  # Stricter gradient clipping
BASELINE_SMOOTHING = 0.05  # Faster baseline adaptation
DROPOUT_RATE = 0.1  # Add dropout for regularization

# Special tokens for reasoning structure
SPECIAL_TOKENS = {
    'PAD': '<PAD>',
    'UNK': '<UNK>',
    'BOS': '<BOS>',
    'EOS': '<EOS>',
    'REASON_START': '<REASON_START>',
    'REASON_END': '<REASON_END>',
    'RESULT_START': '<RESULT_START>',
    'RESULT_END': '<RESULT_END>'
}

# Random seed for reproducibility
SEED = 1234

# File paths
DATA_DIR = "data"
MODELS_DIR = "models_out"
RAW_REASONING_FILE = "data/reasoning_raw.txt"
TRAIN_FILE = "data/train_reasoning.txt"
VAL_FILE = "data/val_reasoning.txt"
VOCAB_FILE = "data/vocab.txt"

# Training hyperparameters
GRADIENT_CLIP_NORM = 5.0
BASELINE_SMOOTHING = 0.01  # For REINFORCE baseline
TEMPERATURE_SAMPLING = 1.0
TOP_K = None  # Set to integer for top-k sampling
TOP_P = None  # Set to float for nucleus sampling

# Validation and evaluation
VAL_FRACTION = 0.1
TEST_FRACTION = 0.1
EVAL_EVERY_N_STEPS = 100
SAVE_EVERY_N_STEPS = 500

# Discriminator training frequency
D_STEPS_PER_G_STEP = 1
