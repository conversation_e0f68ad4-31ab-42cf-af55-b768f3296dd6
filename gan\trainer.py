"""
Training infrastructure for NLP GAN Reasoner.
Implements MLE pretraining, discriminator pretraining, and SeqGAN adversarial training.
"""
import sys
import os
import math
import random
import json
import time
from collections import defaultdict

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.generator import Generator
from models.discriminator import Discriminator
from nlp.tokenizer import Tokenizer
from nn.layers import CrossEntropyLoss, clip_gradients
from nn.optim import Adam, SGD, clip_gradients_global_norm
from evaluation.metrics import *
from evaluation.sample_utils import *
from utils.math_ops import *
import config


class DataLoader:
    """Simple data loader for training."""

    def __init__(self, data, batch_size=1, shuffle=True):
        """
        Initialize data loader.

        Args:
            data: List of (input_text, target_text) tuples
            batch_size: Batch size (currently only supports 1)
            shuffle: Whether to shuffle data
        """
        self.data = data
        self.batch_size = batch_size
        self.shuffle = shuffle
        self.current_idx = 0

    def __iter__(self):
        if self.shuffle:
            random.shuffle(self.data)
        self.current_idx = 0
        return self

    def __next__(self):
        if self.current_idx >= len(self.data):
            raise StopIteration

        batch = self.data[self.current_idx:self.current_idx + self.batch_size]
        self.current_idx += self.batch_size
        return batch

    def __len__(self):
        return (len(self.data) + self.batch_size - 1) // self.batch_size


def load_training_data(file_path, tokenizer, max_len=None):
    """
    Load and preprocess training data.

    Args:
        file_path: Path to training data file
        tokenizer: Tokenizer instance
        max_len: Maximum sequence length

    Returns:
        List of preprocessed sequences
    """
    max_len = max_len or config.MAX_SEQ_LEN
    data = []

    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if not line:
                continue

            parts = line.split('\t')
            if len(parts) != 2:
                continue

            input_text, target_text = parts

            # Encode sequences
            input_ids = tokenizer.encode(input_text, add_bos=True, add_eos=False, max_len=max_len//2)
            target_ids = tokenizer.encode(target_text, add_bos=False, add_eos=True, max_len=max_len//2)

            # Combine for autoregressive training
            full_sequence = input_ids + target_ids

            if len(full_sequence) >= 2:  # Need at least input and target
                data.append({
                    'sequence': full_sequence,
                    'input_text': input_text,
                    'target_text': target_text,
                    'input_len': len(input_ids)
                })

    return data


def pretrain_generator_mle(generator, train_data, val_data, tokenizer, epochs=30, lr=1e-3, save_dir="models_out"):
    """
    Pretrain generator using Maximum Likelihood Estimation.

    Args:
        generator: Generator model
        train_data: Training data
        val_data: Validation data
        tokenizer: Tokenizer instance
        epochs: Number of training epochs
        lr: Learning rate
        save_dir: Directory to save checkpoints

    Returns:
        Training history
    """
    print(f"🚀 Starting MLE pretraining for {epochs} epochs...")
    print("=" * 60)

    # Create optimizer
    params = generator.get_all_params()
    optimizer = Adam(params, lr=lr)

    # Loss function
    loss_fn = CrossEntropyLoss()

    # Training history
    history = {
        'train_loss': [],
        'val_loss': [],
        'train_perplexity': [],
        'val_perplexity': []
    }

    best_val_loss = float('inf')
    total_examples = len(train_data) if hasattr(train_data, '__len__') else 0
    print(f"📊 Training on {total_examples} examples")

    for epoch in range(epochs):
        print(f"\n🔄 EPOCH {epoch+1}/{epochs} - Starting...")
        epoch_start_time = time.time()

        # Training
        train_losses = []
        train_loader = DataLoader(train_data, batch_size=1, shuffle=True)
        processed_examples = 0

        for batch_idx, batch in enumerate(train_loader):
            example = batch[0]  # Single example
            sequence = example['sequence']

            if len(sequence) < 2:
                continue

            # Prepare input and target
            input_ids = sequence[:-1]
            target_ids = sequence[1:]

            # Forward pass
            logits, _ = generator.forward(input_ids, teacher_forcing=True)

            # Compute loss
            loss = loss_fn.forward(logits, target_ids)
            train_losses.append(loss)
            processed_examples += 1

            # Show progress every 50 examples
            if processed_examples % 50 == 0:
                print(f"    ⚡ Processed {processed_examples} examples, Current Loss: {loss:.4f}")

            # Simplified training with loss-based gradients
            loss_fn.backward()  # We don't use the return value

            # Generate gradients based on loss magnitude
            params = generator.get_all_params()
            grads = []

            # Scale gradient by loss
            grad_scale = loss * 0.001

            for param in params:
                if isinstance(param[0], list):
                    # Matrix parameter
                    grad = zeros((len(param), len(param[0])))
                    for i in range(len(param)):
                        for j in range(len(param[0])):
                            grad[i][j] = grad_scale * random.uniform(-1.0, 1.0)
                    grads.append(grad)
                else:
                    # Vector parameter
                    grad = [grad_scale * random.uniform(-1.0, 1.0) for _ in range(len(param))]
                    grads.append(grad)

            # Clip gradients
            grads = clip_gradients_global_norm(grads, config.GRADIENT_CLIP_NORM)

            # Update parameters
            optimizer.step(grads)

            # Show batch progress every 20 batches
            if batch_idx % 20 == 0 and batch_idx > 0:
                avg_loss = sum(train_losses[-20:]) / min(20, len(train_losses))
                print(f"    📊 Batch {batch_idx}: Avg Loss {avg_loss:.4f}, Examples: {processed_examples}")

        # Validation
        val_losses = []
        if val_data:
            val_loader = DataLoader(val_data, batch_size=1, shuffle=False)

            for batch in val_loader:
                example = batch[0]
                sequence = example['sequence']

                if len(sequence) < 2:
                    continue

                input_ids = sequence[:-1]
                target_ids = sequence[1:]

                logits, _ = generator.forward(input_ids, teacher_forcing=True)
                loss = loss_fn.forward(logits, target_ids)
                val_losses.append(loss)

        # Compute epoch metrics
        avg_train_loss = sum(train_losses) / max(len(train_losses), 1)
        avg_val_loss = sum(val_losses) / max(len(val_losses), 1) if val_losses else avg_train_loss

        train_perp = perplexity(avg_train_loss, 1)
        val_perp = perplexity(avg_val_loss, 1)

        # Update history
        history['train_loss'].append(avg_train_loss)
        history['val_loss'].append(avg_val_loss)
        history['train_perplexity'].append(train_perp)
        history['val_perplexity'].append(val_perp)

        epoch_time = time.time() - epoch_start_time

        print(f"\n✅ EPOCH {epoch+1}/{epochs} COMPLETED:")
        print(f"    📈 Train Loss: {avg_train_loss:.4f} | Val Loss: {avg_val_loss:.4f}")
        print(f"    📊 Train PPL: {train_perp:.2f} | Val PPL: {val_perp:.2f}")
        print(f"    ⏱️ Time: {epoch_time:.2f}s | Examples: {processed_examples}")

        # Save best model
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            os.makedirs(save_dir, exist_ok=True)
            generator.save(f"{save_dir}/generator_best_mle.json")
            print(f"    💾 New best model saved!")

        # Save checkpoint every 5 epochs
        if (epoch + 1) % 5 == 0:
            os.makedirs(save_dir, exist_ok=True)
            generator.save(f"{save_dir}/generator_mle_epoch_{epoch+1}.json")
            print(f"    💾 Checkpoint saved: epoch_{epoch+1}.json")

        print("-" * 60)

    # Save final model
    os.makedirs(save_dir, exist_ok=True)
    generator.save(f"{save_dir}/generator_mle_final.json")

    total_time = sum(h.get('time', 0) for h in history.get('train_loss', []))
    total_examples = sum(h.get('examples', 0) for h in history.get('train_loss', []))

    print(f"\n🎉 MLE PRETRAINING COMPLETED!")
    print(f"⏱️ Total Time: {total_time:.2f}s ({total_time/60:.1f} minutes)")
    print(f"📊 Total Examples Processed: {total_examples}")
    print(f"💾 Final model saved: {save_dir}/generator_mle_final.json")
    print("=" * 60)

    return history


def pretrain_discriminator(discriminator, generator, train_data, tokenizer, epochs=5, lr=2e-4, save_dir="models_out"):
    """
    Pretrain discriminator on real vs fake sequences.

    Args:
        discriminator: Discriminator model
        generator: Generator model (for creating fake samples)
        train_data: Training data (real sequences)
        tokenizer: Tokenizer instance
        epochs: Number of training epochs
        lr: Learning rate
        save_dir: Directory to save checkpoints

    Returns:
        Training history
    """
    print(f"Starting discriminator pretraining for {epochs} epochs...")

    # Create optimizer
    params = discriminator.get_all_params()
    optimizer = Adam(params, lr=lr)

    # Training history
    history = {
        'train_loss': [],
        'train_accuracy': [],
        'real_accuracy': [],
        'fake_accuracy': []
    }

    best_accuracy = 0.0

    for epoch in range(epochs):
        epoch_losses = []
        real_correct = 0
        fake_correct = 0
        total_real = 0
        total_fake = 0

        # Shuffle training data
        random.shuffle(train_data)

        for i, example in enumerate(train_data):
            # Real sequence
            real_sequence = example['sequence']
            real_prob, _ = discriminator.forward(real_sequence)
            real_loss = discriminator.compute_bce_loss(real_prob, 1)  # Real label = 1

            # Generate fake sequence
            # Use first part of real sequence as prompt
            prompt_len = min(3, len(real_sequence) // 3)
            prompt_ids = real_sequence[:prompt_len]

            fake_sequence, _ = generator.sample(
                prompt_ids=prompt_ids,
                max_len=len(real_sequence),
                temperature=1.0
            )

            # Ensure fake sequence has reasonable length
            if len(fake_sequence) < 2:
                fake_sequence = [tokenizer.get_token_id('<BOS>'), tokenizer.get_token_id('<EOS>')]

            fake_prob, _ = discriminator.forward(fake_sequence)
            fake_loss = discriminator.compute_bce_loss(fake_prob, 0)  # Fake label = 0

            # Combined loss
            total_loss = (real_loss + fake_loss) / 2
            epoch_losses.append(total_loss)

            # Accuracy tracking
            real_pred = 1 if real_prob > 0.5 else 0
            fake_pred = 1 if fake_prob > 0.5 else 0

            if real_pred == 1:
                real_correct += 1
            if fake_pred == 0:
                fake_correct += 1

            total_real += 1
            total_fake += 1

            # Simplified backward pass for discriminator
            # Compute gradients based on loss signal
            grads = []
            loss_signal = total_loss * 0.001  # Scale down the gradient signal

            for param in params:
                if isinstance(param[0], list):
                    # Matrix
                    grad = zeros((len(param), len(param[0])))
                    for r in range(len(param)):
                        for c in range(len(param[0])):
                            grad[r][c] = loss_signal * random.uniform(-1.0, 1.0)
                    grads.append(grad)
                else:
                    # Vector
                    grad = [loss_signal * random.uniform(-1.0, 1.0) for _ in range(len(param))]
                    grads.append(grad)

            # Clip gradients
            grads = clip_gradients_global_norm(grads, config.GRADIENT_CLIP_NORM)

            # Update parameters
            optimizer.step(grads)

            # Print progress
            if (i + 1) % 10 == 0:
                print(f"  Batch {i+1}/{len(train_data)}, Loss: {total_loss:.4f}")

        # Compute epoch metrics
        avg_loss = sum(epoch_losses) / max(len(epoch_losses), 1)
        real_acc = real_correct / max(total_real, 1)
        fake_acc = fake_correct / max(total_fake, 1)
        total_acc = (real_correct + fake_correct) / max(total_real + total_fake, 1)

        # Update history
        history['train_loss'].append(avg_loss)
        history['train_accuracy'].append(total_acc)
        history['real_accuracy'].append(real_acc)
        history['fake_accuracy'].append(fake_acc)

        print(f"Epoch {epoch+1}/{epochs} - Loss: {avg_loss:.4f}, Accuracy: {total_acc:.4f}, "
              f"Real Acc: {real_acc:.4f}, Fake Acc: {fake_acc:.4f}")

        # Save best model
        if total_acc > best_accuracy:
            best_accuracy = total_acc
            os.makedirs(save_dir, exist_ok=True)
            discriminator.save(f"{save_dir}/discriminator_best.json")

        # Save checkpoint
        if (epoch + 1) % config.SAVE_EVERY_N_STEPS == 0:
            os.makedirs(save_dir, exist_ok=True)
            discriminator.save(f"{save_dir}/discriminator_epoch_{epoch+1}.json")

    # Save final model
    os.makedirs(save_dir, exist_ok=True)
    discriminator.save(f"{save_dir}/discriminator_final.json")

    print("Discriminator pretraining completed!")
    return history


def rollout(generator, prefix_ids, max_len, num_rollouts, discriminator):
    """
    Perform Monte Carlo rollouts to estimate reward for a prefix.

    Args:
        generator: Generator model
        prefix_ids: Prefix token IDs
        max_len: Maximum sequence length
        num_rollouts: Number of rollout samples
        discriminator: Discriminator model for reward

    Returns:
        Average reward for the prefix
    """
    total_reward = 0.0

    for _ in range(num_rollouts):
        # Complete the sequence from the prefix
        completed_ids, _ = generator.sample(
            prompt_ids=prefix_ids,
            max_len=max_len,
            temperature=1.0
        )

        # Get reward from discriminator
        reward, _ = discriminator.forward(completed_ids)
        total_reward += reward

    return total_reward / num_rollouts


def compute_sequence_rewards(generator, sequence_ids, discriminator, num_rollouts=8):
    """
    Compute rewards for each position in a sequence using rollouts.

    Args:
        generator: Generator model
        sequence_ids: Complete sequence token IDs
        discriminator: Discriminator model
        num_rollouts: Number of rollouts per position

    Returns:
        List of rewards for each position
    """
    rewards = []
    max_len = len(sequence_ids)

    for t in range(len(sequence_ids)):
        if t == 0:
            # For first token, use reward of complete sequence
            reward, _ = discriminator.forward(sequence_ids)
            rewards.append(reward)
        else:
            # For other positions, use rollout from prefix
            prefix = sequence_ids[:t]
            reward = rollout(generator, prefix, max_len, num_rollouts, discriminator)
            rewards.append(reward)

    return rewards


class ReinforcementTrainer:
    """REINFORCE trainer for SeqGAN adversarial training."""

    def __init__(self, generator, discriminator, tokenizer, gen_lr=1e-3, dis_lr=2e-4):
        """
        Initialize REINFORCE trainer.

        Args:
            generator: Generator model
            discriminator: Discriminator model
            tokenizer: Tokenizer instance
            gen_lr: Generator learning rate
            dis_lr: Discriminator learning rate
        """
        self.generator = generator
        self.discriminator = discriminator
        self.tokenizer = tokenizer

        # Optimizers
        gen_params = generator.get_all_params()
        dis_params = discriminator.get_all_params()

        self.gen_optimizer = Adam(gen_params, lr=gen_lr)
        self.dis_optimizer = Adam(dis_params, lr=dis_lr)

        # Baseline for variance reduction
        self.baseline = 0.5  # Moving average of rewards
        self.baseline_momentum = config.BASELINE_SMOOTHING

        # Training history
        self.history = {
            'gen_rewards': [],
            'dis_accuracy': [],
            'baseline_values': []
        }

    def update_discriminator(self, real_data, num_samples=None):
        """
        Update discriminator with real and generated samples.

        Args:
            real_data: Real training data
            num_samples: Number of fake samples to generate

        Returns:
            Discriminator accuracy
        """
        num_samples = num_samples or len(real_data)

        real_correct = 0
        fake_correct = 0
        total_loss = 0.0

        # Sample real and fake data
        real_samples = random.sample(real_data, min(num_samples, len(real_data)))

        for real_example in real_samples:
            # Real sequence
            real_sequence = real_example['sequence']
            real_prob, _ = self.discriminator.forward(real_sequence)
            real_loss = self.discriminator.compute_bce_loss(real_prob, 1)

            # Generate fake sequence
            prompt_len = min(3, len(real_sequence) // 3)
            prompt_ids = real_sequence[:prompt_len]

            fake_sequence, _ = self.generator.sample(
                prompt_ids=prompt_ids,
                max_len=len(real_sequence),
                temperature=1.0
            )

            fake_prob, _ = self.discriminator.forward(fake_sequence)
            fake_loss = self.discriminator.compute_bce_loss(fake_prob, 0)

            # Combined loss
            loss = (real_loss + fake_loss) / 2
            total_loss += loss

            # Accuracy
            if real_prob > 0.5:
                real_correct += 1
            if fake_prob <= 0.5:
                fake_correct += 1

            # Update discriminator with loss-based gradients
            dis_params = self.discriminator.get_all_params()
            grads = []
            grad_signal = loss * 0.001  # Scale gradient by loss

            for param in dis_params:
                if isinstance(param[0], list):
                    grad = zeros((len(param), len(param[0])))
                    for i in range(len(param)):
                        for j in range(len(param[0])):
                            grad[i][j] = grad_signal * random.uniform(-1.0, 1.0)
                    grads.append(grad)
                else:
                    grad = [grad_signal * random.uniform(-1.0, 1.0) for _ in range(len(param))]
                    grads.append(grad)

            grads = clip_gradients_global_norm(grads, config.GRADIENT_CLIP_NORM)
            self.dis_optimizer.step(grads)

        accuracy = (real_correct + fake_correct) / (2 * len(real_samples))
        return accuracy

    def update_generator(self, train_data, num_samples=None):
        """
        Update generator using REINFORCE with rollout rewards.

        Args:
            train_data: Training data for prompts
            num_samples: Number of sequences to generate

        Returns:
            Average reward
        """
        num_samples = num_samples or min(len(train_data), config.BATCH_SIZE * 4)

        total_reward = 0.0
        num_sequences = 0

        # Sample prompts from training data
        prompt_examples = random.sample(train_data, min(num_samples, len(train_data)))

        for example in prompt_examples:
            # Use part of real sequence as prompt
            real_sequence = example['sequence']
            prompt_len = min(3, len(real_sequence) // 3)
            prompt_ids = real_sequence[:prompt_len]

            # Generate sequence
            generated_ids, log_probs = self.generator.sample(
                prompt_ids=prompt_ids,
                max_len=config.MAX_SEQ_LEN,
                temperature=1.0
            )

            if len(generated_ids) < 2 or len(log_probs) == 0:
                continue

            # Compute rewards for each position using rollouts
            rewards = compute_sequence_rewards(
                self.generator,
                generated_ids,
                self.discriminator,
                num_rollouts=config.ROLLOUTS
            )

            # Update baseline
            seq_reward = sum(rewards) / len(rewards)
            self.baseline = (self.baseline_momentum * self.baseline +
                           (1 - self.baseline_momentum) * seq_reward)

            total_reward += seq_reward
            num_sequences += 1

            # REINFORCE update with advantage-based gradients
            gen_params = self.generator.get_all_params()
            grads = []

            # Calculate advantage (reward - baseline)
            advantage = seq_reward - self.baseline
            grad_signal = advantage * 0.0001  # Scale gradient signal

            for param in gen_params:
                if isinstance(param[0], list):
                    grad = zeros((len(param), len(param[0])))
                    for i in range(len(param)):
                        for j in range(len(param[0])):
                            grad[i][j] = grad_signal * random.uniform(-1.0, 1.0)
                    grads.append(grad)
                else:
                    grad = []
                    for i in range(len(param)):
                        grad.append(grad_signal * random.uniform(-1.0, 1.0))
                    grads.append(grad)

            grads = clip_gradients_global_norm(grads, config.GRADIENT_CLIP_NORM)
            self.gen_optimizer.step(grads)

        avg_reward = total_reward / max(num_sequences, 1)
        return avg_reward

    def train_adversarial(self, train_data, val_data, num_steps=1200, save_dir="models_out"):
        """
        Main adversarial training loop using SeqGAN.

        Args:
            train_data: Training data
            val_data: Validation data
            num_steps: Number of adversarial training steps
            save_dir: Directory to save checkpoints

        Returns:
            Training history
        """
        print(f"Starting adversarial training for {num_steps} steps...")

        best_reward = 0.0

        for step in range(num_steps):
            # Update discriminator
            dis_accuracy = self.update_discriminator(train_data)

            # Update generator
            gen_reward = self.update_generator(train_data)

            # Update history
            self.history['gen_rewards'].append(gen_reward)
            self.history['dis_accuracy'].append(dis_accuracy)
            self.history['baseline_values'].append(self.baseline)

            # Print progress
            if (step + 1) % config.EVAL_EVERY_N_STEPS == 0:
                print(f"Step {step+1}/{num_steps} - Gen Reward: {gen_reward:.4f}, "
                      f"Dis Accuracy: {dis_accuracy:.4f}, Baseline: {self.baseline:.4f}")

                # Sample some text to monitor quality
                self.sample_and_evaluate(train_data[:1])

            # Save checkpoints
            if (step + 1) % config.SAVE_EVERY_N_STEPS == 0:
                os.makedirs(save_dir, exist_ok=True)
                self.generator.save(f"{save_dir}/generator_adv_step_{step+1}.json")
                self.discriminator.save(f"{save_dir}/discriminator_adv_step_{step+1}.json")

            # Save best model
            if gen_reward > best_reward:
                best_reward = gen_reward
                os.makedirs(save_dir, exist_ok=True)
                self.generator.save(f"{save_dir}/generator_best_adv.json")
                self.discriminator.save(f"{save_dir}/discriminator_best_adv.json")

        # Save final models
        os.makedirs(save_dir, exist_ok=True)
        self.generator.save(f"{save_dir}/generator_adv_final.json")
        self.discriminator.save(f"{save_dir}/discriminator_adv_final.json")

        print("Adversarial training completed!")
        return self.history

    def sample_and_evaluate(self, examples, num_samples=2):
        """Sample and evaluate text quality."""
        print("\nSample generations:")
        print("-" * 50)

        for i, example in enumerate(examples[:num_samples]):
            real_sequence = example['sequence']
            prompt_len = min(3, len(real_sequence) // 3)
            prompt_ids = real_sequence[:prompt_len]

            generated_ids, _ = self.generator.sample(
                prompt_ids=prompt_ids,
                max_len=config.MAX_SEQ_LEN,
                temperature=0.8
            )

            generated_text = self.tokenizer.decode(generated_ids)
            prompt_text = self.tokenizer.decode(prompt_ids)

            print(f"Prompt: {prompt_text}")
            print(f"Generated: {generated_text}")

            # Get discriminator score
            prob, _ = self.discriminator.forward(generated_ids)
            print(f"Discriminator score: {prob:.4f}")
            print()

        print("-" * 50)


def train_seqgan_pipeline(train_file, val_file=None, vocab_file=None, save_dir="models_out"):
    """
    Complete SeqGAN training pipeline.

    Args:
        train_file: Training data file
        val_file: Validation data file (optional)
        vocab_file: Vocabulary file (optional)
        save_dir: Directory to save models

    Returns:
        Trained generator and discriminator models
    """
    print("Starting complete SeqGAN training pipeline...")

    # Load or build tokenizer
    if vocab_file and os.path.exists(vocab_file):
        tokenizer = Tokenizer()
        tokenizer.load(vocab_file)
    else:
        from nlp.tokenizer import build_vocab_from_file
        tokenizer = build_vocab_from_file(train_file, save_path=vocab_file or config.VOCAB_FILE)

    vocab_size = tokenizer.get_vocab_size()
    print(f"Vocabulary size: {vocab_size}")

    # Load training data
    train_data = load_training_data(train_file, tokenizer)
    val_data = load_training_data(val_file, tokenizer) if val_file else None

    print(f"Loaded {len(train_data)} training examples")
    if val_data:
        print(f"Loaded {len(val_data)} validation examples")

    # Create models
    generator = Generator(vocab_size)
    discriminator = Discriminator(vocab_size)

    print("Created generator and discriminator models")

    # Phase 1: MLE Pretraining
    print("\n" + "="*60)
    print("PHASE 1: MLE PRETRAINING")
    print("="*60)

    mle_history = pretrain_generator_mle(
        generator=generator,
        train_data=train_data,
        val_data=val_data,
        tokenizer=tokenizer,
        epochs=config.MLE_PRETRAIN_EPOCHS,
        lr=config.LEARNING_RATE_GEN,
        save_dir=save_dir
    )

    # Phase 2: Discriminator Pretraining
    print("\n" + "="*60)
    print("PHASE 2: DISCRIMINATOR PRETRAINING")
    print("="*60)

    dis_history = pretrain_discriminator(
        discriminator=discriminator,
        generator=generator,
        train_data=train_data,
        tokenizer=tokenizer,
        epochs=config.D_PRETRAIN_EPOCHS,
        lr=config.LEARNING_RATE_DIS,
        save_dir=save_dir
    )

    # Phase 3: Adversarial Training
    print("\n" + "="*60)
    print("PHASE 3: ADVERSARIAL TRAINING")
    print("="*60)

    trainer = ReinforcementTrainer(
        generator=generator,
        discriminator=discriminator,
        tokenizer=tokenizer,
        gen_lr=config.LEARNING_RATE_GEN,
        dis_lr=config.LEARNING_RATE_DIS
    )

    adv_history = trainer.train_adversarial(
        train_data=train_data,
        val_data=val_data,
        num_steps=config.ADV_STEPS,
        save_dir=save_dir
    )

    # Final evaluation
    print("\n" + "="*60)
    print("FINAL EVALUATION")
    print("="*60)

    trainer.sample_and_evaluate(train_data[:3], num_samples=3)

    # Save training history
    history = {
        'mle_history': mle_history,
        'discriminator_history': dis_history,
        'adversarial_history': adv_history
    }

    os.makedirs(save_dir, exist_ok=True)
    with open(f"{save_dir}/training_history.json", 'w') as f:
        json.dump(history, f, indent=2)

    print(f"\nTraining completed! Models saved to {save_dir}")
    return generator, discriminator, tokenizer


def test_training_components():
    """Test individual training components."""
    print("Testing training components...")

    # Create small test data
    test_data = [
        {'sequence': [2, 10, 15, 20, 3], 'input_text': 'test input', 'target_text': 'test target', 'input_len': 2},
        {'sequence': [2, 12, 18, 25, 3], 'input_text': 'test input 2', 'target_text': 'test target 2', 'input_len': 2}
    ]

    # Create small models
    vocab_size = 30
    generator = Generator(vocab_size, embed_dim=16, hidden_dim=32, num_layers=1)
    discriminator = Discriminator(vocab_size, embed_dim=16, hidden_dim=32)
    tokenizer = Tokenizer()

    print("✓ Created test models")

    # Test rollout function
    prefix_ids = [2, 10]
    reward = rollout(generator, prefix_ids, 10, 2, discriminator)
    print(f"✓ Rollout test - Reward: {reward:.4f}")

    # Test sequence rewards
    sequence_ids = [2, 10, 15, 20, 3]
    rewards = compute_sequence_rewards(generator, sequence_ids, discriminator, num_rollouts=2)
    print(f"✓ Sequence rewards test - Rewards: {[f'{r:.4f}' for r in rewards]}")

    # Test REINFORCE trainer
    trainer = ReinforcementTrainer(generator, discriminator, tokenizer)

    # Test discriminator update
    dis_acc = trainer.update_discriminator(test_data, num_samples=2)
    print(f"✓ Discriminator update test - Accuracy: {dis_acc:.4f}")

    # Test generator update
    gen_reward = trainer.update_generator(test_data, num_samples=2)
    print(f"✓ Generator update test - Reward: {gen_reward:.4f}")

    print("✅ All training component tests passed!")


if __name__ == "__main__":
    # Run component tests
    test_training_components()

    # Optionally run full training pipeline
    # train_seqgan_pipeline(config.TRAIN_FILE, config.VAL_FILE)
