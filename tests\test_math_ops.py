"""
Unit tests for mathematical operations in utils/math_ops.py
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.math_ops import *
import math


def test_zeros():
    """Test zeros function."""
    # Test 1D
    z1 = zeros(3)
    assert z1 == [0.0, 0.0, 0.0], f"Expected [0.0, 0.0, 0.0], got {z1}"

    # Test 2D
    z2 = zeros((2, 3))
    expected = [[0.0, 0.0, 0.0], [0.0, 0.0, 0.0]]
    assert z2 == expected, f"Expected {expected}, got {z2}"

    print("✓ test_zeros passed")


def test_ones():
    """Test ones function."""
    # Test 1D
    o1 = ones(3)
    assert o1 == [1.0, 1.0, 1.0], f"Expected [1.0, 1.0, 1.0], got {o1}"

    # Test 2D
    o2 = ones((2, 3))
    expected = [[1.0, 1.0, 1.0], [1.0, 1.0, 1.0]]
    assert o2 == expected, f"Expected {expected}, got {o2}"

    print("✓ test_ones passed")


def test_matmul():
    """Test matrix multiplication."""
    # Test case 1: 2x3 @ 3x2 = 2x2
    A = [[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]]
    B = [[1.0, 2.0], [3.0, 4.0], [5.0, 6.0]]

    C = matmul(A, B)
    expected = [[22.0, 28.0], [49.0, 64.0]]

    for i in range(len(expected)):
        for j in range(len(expected[0])):
            assert abs(C[i][j] - expected[i][j]) < 1e-6, f"Mismatch at [{i}][{j}]: {C[i][j]} vs {expected[i][j]}"

    print("✓ test_matmul passed")


def test_matvec():
    """Test matrix-vector multiplication."""
    A = [[1.0, 2.0], [3.0, 4.0], [5.0, 6.0]]
    v = [2.0, 3.0]

    result = matvec(A, v)
    expected = [8.0, 18.0, 28.0]  # [1*2+2*3, 3*2+4*3, 5*2+6*3]

    for i in range(len(expected)):
        assert abs(result[i] - expected[i]) < 1e-6, f"Mismatch at [{i}]: {result[i]} vs {expected[i]}"

    print("✓ test_matvec passed")


def test_transpose():
    """Test matrix transpose."""
    A = [[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]]
    AT = transpose(A)
    expected = [[1.0, 4.0], [2.0, 5.0], [3.0, 6.0]]

    assert AT == expected, f"Expected {expected}, got {AT}"

    print("✓ test_transpose passed")


def test_add_sub():
    """Test addition and subtraction."""
    # Vector operations
    a = [1.0, 2.0, 3.0]
    b = [4.0, 5.0, 6.0]

    c = add(a, b)
    assert c == [5.0, 7.0, 9.0], f"Vector add failed: {c}"

    d = sub(a, b)
    assert d == [-3.0, -3.0, -3.0], f"Vector sub failed: {d}"

    # Matrix operations
    A = [[1.0, 2.0], [3.0, 4.0]]
    B = [[5.0, 6.0], [7.0, 8.0]]

    C = add(A, B)
    expected_add = [[6.0, 8.0], [10.0, 12.0]]
    assert C == expected_add, f"Matrix add failed: {C}"

    D = sub(A, B)
    expected_sub = [[-4.0, -4.0], [-4.0, -4.0]]
    assert D == expected_sub, f"Matrix sub failed: {D}"

    print("✓ test_add_sub passed")


def test_scalar_mul():
    """Test scalar multiplication."""
    # Vector
    v = [1.0, 2.0, 3.0]
    result = scalar_mul(v, 2.0)
    assert result == [2.0, 4.0, 6.0], f"Vector scalar mul failed: {result}"

    # Matrix
    A = [[1.0, 2.0], [3.0, 4.0]]
    result = scalar_mul(A, 3.0)
    expected = [[3.0, 6.0], [9.0, 12.0]]
    assert result == expected, f"Matrix scalar mul failed: {result}"

    print("✓ test_scalar_mul passed")


def test_softmax():
    """Test softmax function."""
    # Test basic softmax
    vec = [1.0, 2.0, 3.0]
    probs = softmax_row(vec)

    # Check probabilities sum to 1
    total = sum(probs)
    assert abs(total - 1.0) < 1e-6, f"Softmax doesn't sum to 1: {total}"

    # Check all probabilities are positive
    for p in probs:
        assert p > 0, f"Negative probability: {p}"

    # Test numerical stability with large numbers
    large_vec = [1000.0, 1001.0, 1002.0]
    large_probs = softmax_row(large_vec)
    large_total = sum(large_probs)
    assert abs(large_total - 1.0) < 1e-6, f"Large softmax doesn't sum to 1: {large_total}"

    # Test softmax_rows
    matrix = [[1.0, 2.0, 3.0], [4.0, 5.0, 6.0]]
    prob_matrix = softmax_rows(matrix)

    for row in prob_matrix:
        row_sum = sum(row)
        assert abs(row_sum - 1.0) < 1e-6, f"Row doesn't sum to 1: {row_sum}"

    print("✓ test_softmax passed")


def test_elementwise_mul():
    """Test element-wise multiplication."""
    # Vector
    a = [1.0, 2.0, 3.0]
    b = [4.0, 5.0, 6.0]
    result = elementwise_mul(a, b)
    assert result == [4.0, 10.0, 18.0], f"Vector elementwise mul failed: {result}"

    # Matrix
    A = [[1.0, 2.0], [3.0, 4.0]]
    B = [[5.0, 6.0], [7.0, 8.0]]
    result = elementwise_mul(A, B)
    expected = [[5.0, 12.0], [21.0, 32.0]]
    assert result == expected, f"Matrix elementwise mul failed: {result}"

    print("✓ test_elementwise_mul passed")


def test_argmax():
    """Test argmax function."""
    vec = [1.0, 5.0, 3.0, 2.0]
    idx = argmax(vec)
    assert idx == 1, f"Expected argmax index 1, got {idx}"

    vec2 = [10.0, 2.0, 3.0]
    idx2 = argmax(vec2)
    assert idx2 == 0, f"Expected argmax index 0, got {idx2}"

    print("✓ test_argmax passed")


def test_clip():
    """Test clipping functions."""
    # Single value
    assert clip(5.0, 0.0, 3.0) == 3.0
    assert clip(-2.0, 0.0, 3.0) == 0.0
    assert clip(1.5, 0.0, 3.0) == 1.5

    # Vector
    vec = [-1.0, 2.0, 5.0]
    clipped = clip_vec(vec, 0.0, 3.0)
    assert clipped == [0.0, 2.0, 3.0], f"Vector clip failed: {clipped}"

    print("✓ test_clip passed")


def test_norm():
    """Test vector norm."""
    vec = [3.0, 4.0]  # Should have norm 5.0
    n = norm(vec)
    assert abs(n - 5.0) < 1e-6, f"Expected norm 5.0, got {n}"

    # Test normalization
    normalized = normalize_vec(vec)
    norm_of_normalized = norm(normalized)
    assert abs(norm_of_normalized - 1.0) < 1e-6, f"Normalized vector norm should be 1.0, got {norm_of_normalized}"

    print("✓ test_norm passed")


def run_all_tests():
    """Run all math operation tests."""
    print("Running math operations tests...")

    test_zeros()
    test_ones()
    test_matmul()
    test_matvec()
    test_transpose()
    test_add_sub()
    test_scalar_mul()
    test_softmax()
    test_elementwise_mul()
    test_argmax()
    test_clip()
    test_norm()

    print("\n✅ All math operations tests passed!")


if __name__ == "__main__":
    run_all_tests()
