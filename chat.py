"""
UNIFIED Chat Interface for NLP GAN Reasoner
Auto-detects and supports both neural models and n-gram models.

Usage:
  python chat.py                    # Interactive chat
  python chat.py --single "Hello"   # Single prompt
  python chat.py --model path.json  # Specify model
"""
import sys
import os
import random
import argparse
import json
from collections import defaultdict, Counter

# Add current directory to path
sys.path.append('.')

# Import core components
from models.generator import Generator
from nlp.tokenizer import Tokenizer
from train import SimpleNGramModel


class UnifiedChatAgent:
    """Unified chat agent that auto-detects neural vs n-gram models."""

    def __init__(self, model_path=None, vocab_path=None):
        self.vocab_path = vocab_path or "data/vocab.txt"

        # Auto-detect model type
        if model_path is None:
            # Try n-gram model first (faster)
            if os.path.exists("models_out/simple_model.json"):
                model_path = "models_out/simple_model.json"
                self.model_type = "ngram"
            elif os.path.exists("models_out/generator_final.json"):
                model_path = "models_out/generator_final.json"
                self.model_type = "neural"
            else:
                raise FileNotFoundError("No trained model found! Run: python train.py --level fast")
        else:
            # Detect model type from filename
            if "simple" in model_path:
                self.model_type = "ngram"
            else:
                self.model_type = "neural"

        self.model_path = model_path

        # Load tokenizer
        print("🔄 Loading tokenizer...")
        self.tokenizer = Tokenizer()
        self.tokenizer.load(self.vocab_path)
        vocab_size = self.tokenizer.get_vocab_size()
        print(f"✅ Loaded vocabulary: {vocab_size} tokens")

        # Load model based on type
        print(f"🔄 Loading {self.model_type.upper()} model...")

        if self.model_type == "ngram":
            self.model = SimpleNGramModel(vocab_size)
            self.model.load(self.model_path)
            print(f"✅ Loaded n-gram model: {self.model_path}")
        else:
            # Neural model
            self.model = Generator(
                vocab_size=vocab_size,
                embed_dim=64,      # Match training configuration
                hidden_dim=128,    # Match training configuration
                num_layers=1       # Match training configuration
            )
            self.model.load(self.model_path)
            print(f"✅ Loaded neural model: {self.model_path}")

        print(f"🤖 Chat agent ready! Using {self.model_type.upper()} model.")

    def generate_response(self, user_input, max_length=30):
        """Generate response to user input."""
        try:
            # Tokenize input
            input_tokens = self.tokenizer.encode(user_input, add_bos=True, add_eos=False, max_len=15)

            if not input_tokens:
                return "I didn't understand that. Could you try again?"

            # Generate response based on model type
            if self.model_type == "ngram":
                full_sequence = self.model.generate_sequence(input_tokens, max_length=max_length)
                # Extract only the new tokens (continuation)
                response_tokens = full_sequence[len(input_tokens):]
            else:
                # Neural model generation
                response_tokens, _ = self.model.sample(prompt_ids=input_tokens, max_len=max_length, temperature=0.8)

            # Decode response
            response_text = self.tokenizer.decode(response_tokens)

            # Clean up response
            response_text = response_text.replace('<BOS>', '').replace('<EOS>', '').strip()

            # Add reasoning context for reasoning prompts
            if any(keyword in user_input.lower() for keyword in ['given:', 'if ', 'premise:', 'therefore']):
                if not response_text.startswith(('Therefore:', 'Given:', 'If ', 'Premise:')):
                    response_text = f": {response_text}"

            return response_text if response_text else "I'm thinking..."

        except Exception as e:
            return f"I encountered an issue processing that. Could you try rephrasing? (Error: {str(e)[:50]})"

    def chat_loop(self):
        """Interactive chat loop."""
        print("\n🤖 NLP GAN Reasoner - Dynamic Chat")
        print("=" * 50)
        print(f"✅ Using REAL AI generation with {self.model_type.upper()} model")
        print("✅ Supports reasoning, questions, and general conversation")
        print("Type 'help' for examples, 'quit' to exit")
        print("-" * 50)

        while True:
            try:
                user_input = input("\n👤 You: ").strip()

                if user_input.lower() in ['quit', 'exit', 'bye']:
                    print("👋 Goodbye! Thanks for chatting with me!")
                    break
                elif user_input.lower() == 'help':
                    print("🔍 Try these examples:")
                    print("  • 'Given: A person is running'")
                    print("  • 'If it rains'")
                    print("  • 'What is AI?'")
                    print("  • 'I think that'")
                    print("  • 'Hello world'")
                    continue
                elif not user_input:
                    continue

                # Generate and display response
                response = self.generate_response(user_input)
                print(f"🤖 Bot: {response}")

            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Unified NLP Chat Agent")
    parser.add_argument("--single", type=str, help="Single prompt mode")
    parser.add_argument("--model", type=str, help="Model file path")
    parser.add_argument("--vocab", type=str, help="Vocabulary file path")

    args = parser.parse_args()

    try:
        # Create chat agent
        agent = UnifiedChatAgent(model_path=args.model, vocab_path=args.vocab)

        if args.single:
            # Single prompt mode
            print(f"\n👤 Prompt: {args.single}")
            response = agent.generate_response(args.single)
            print(f"🤖 Response: {response}")
        else:
            # Interactive chat mode
            agent.chat_loop()

    except FileNotFoundError as e:
        print(f"❌ File not found: {e}")
        print("💡 Make sure to run training first!")
        print("🚀 Quick start: python train.py --level fast")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()