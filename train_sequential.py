"""
SEQUENTIAL NLP REASONING TRAINING
Proper 3-stage training: NLP → Reasoning → GAN

This implements the correct architecture:
1. Stage 1: Train base NLP model on general language
2. Stage 2: Fine-tune for reasoning capabilities  
3. Stage 3: Use GAN to improve quality

Usage:
  python train_sequential.py --stage 1    # Train base NLP
  python train_sequential.py --stage 2    # Add reasoning
  python train_sequential.py --stage 3    # GAN refinement
  python train_sequential.py --all        # Run all stages
"""
import sys
import os
import time
import random
import math
import json
import re
import argparse
from collections import defaultdict, Counter

# Add current directory to path
sys.path.append('.')

# Import components
from models.generator import Generator
from models.discriminator import Discriminator
from nlp.tokenizer import Tokenizer
from nn.optim import Adam
from nn.layers import CrossEntropyLoss
from gan.trainer import ReinforcementTrainer

class SequentialTrainer:
    """Sequential training pipeline for proper reasoning development."""
    
    def __init__(self, config):
        self.config = config
        self.tokenizer = None
        self.generator = None
        self.discriminator = None
        
    def stage_1_base_nlp(self):
        """Stage 1: Train base NLP model on general language only."""
        print("🧠 STAGE 1: Training Base NLP Model")
        print("=" * 60)
        print("📚 Training on general language patterns only")
        print("🎯 Goal: Learn basic language structure and vocabulary")
        
        # Load and process ONLY raw text data
        raw_text_examples = self._load_raw_text_data()
        print(f"📖 Loaded {len(raw_text_examples)} general language examples")
        
        # Build vocabulary from general text
        self._build_vocabulary(raw_text_examples)
        
        # Train base model
        self._train_base_model(raw_text_examples)
        
        # Save stage 1 model
        self.generator.save("models_out/stage1_base_nlp.json")
        print("✅ Stage 1 Complete: Base NLP model saved")
        
    def stage_2_reasoning(self):
        """Stage 2: Fine-tune for reasoning capabilities."""
        print("\n🔗 STAGE 2: Adding Reasoning Capabilities")
        print("=" * 60)
        print("📚 Fine-tuning on structured reasoning patterns")
        print("🎯 Goal: Learn logical reasoning (Given/Therefore, If/Then)")
        
        # Load stage 1 model
        if os.path.exists("models_out/stage1_base_nlp.json"):
            self.generator.load("models_out/stage1_base_nlp.json")
            print("✅ Loaded Stage 1 base model")
        else:
            print("❌ Stage 1 model not found! Run stage 1 first.")
            return
            
        # Load and process ONLY reasoning data
        reasoning_examples = self._load_reasoning_data()
        print(f"🧠 Loaded {len(reasoning_examples)} reasoning examples")
        
        # Fine-tune on reasoning
        self._fine_tune_reasoning(reasoning_examples)
        
        # Save stage 2 model
        self.generator.save("models_out/stage2_reasoning.json")
        print("✅ Stage 2 Complete: Reasoning model saved")
        
    def stage_3_gan_refinement(self):
        """Stage 3: Use GAN to improve quality."""
        print("\n🎯 STAGE 3: GAN Quality Refinement")
        print("=" * 60)
        print("🥊 Training discriminator to improve generation quality")
        print("🎯 Goal: Polish reasoning quality with adversarial training")
        
        # Load stage 2 model
        if os.path.exists("models_out/stage2_reasoning.json"):
            self.generator.load("models_out/stage2_reasoning.json")
            print("✅ Loaded Stage 2 reasoning model")
        else:
            print("❌ Stage 2 model not found! Run stage 2 first.")
            return
            
        # Initialize discriminator
        vocab_size = self.tokenizer.get_vocab_size()
        self.discriminator = Discriminator(
            vocab_size=vocab_size,
            embed_dim=self.config['embed_dim'],
            hidden_dim=self.config['hidden_dim']
        )
        
        # Load combined data for GAN training
        all_examples = self._load_all_data()
        print(f"📚 Using {len(all_examples)} examples for GAN training")
        
        # GAN training
        self._train_gan(all_examples)
        
        # Save final model
        self.generator.save("models_out/stage3_final.json")
        self.discriminator.save("models_out/discriminator_final.json")
        print("✅ Stage 3 Complete: Final GAN-refined model saved")
        
    def _load_raw_text_data(self):
        """Load only general language data."""
        examples = []
        
        if os.path.exists("data/raw_text.txt"):
            with open("data/raw_text.txt", 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            
            for line in lines[:self.config['max_examples']//2]:
                line = line.strip()
                if len(line) > 10 and len(line) < 150:
                    words = line.split()
                    if len(words) >= 4:
                        # Create input-target pairs
                        for i in range(2, min(len(words), 8)):
                            input_text = ' '.join(words[:i])
                            target_text = words[i]
                            examples.append((input_text, target_text))
                            
                            if len(examples) >= self.config['max_examples']//2:
                                break
                if len(examples) >= self.config['max_examples']//2:
                    break
                    
        return examples
        
    def _load_reasoning_data(self):
        """Load only structured reasoning data."""
        examples = []
        
        if os.path.exists("data/reasoning_raw.txt"):
            with open("data/reasoning_raw.txt", 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Extract reasoning patterns
            patterns = [
                (r'Given: (.+?)\. Therefore: (.+?)\.', 'given_therefore'),
                (r'If (.+?)\., then (.+?)\.', 'if_then'),
                (r'If (.+?)\., then NOT \((.+?)\.\)', 'if_then_not'),
                (r'Premise: (.+?)\. Contradicts: (.+?)\.', 'premise_contradicts'),
            ]
            
            for pattern, pattern_type in patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                
                for match in matches:
                    premise, conclusion = match
                    
                    if pattern_type == 'given_therefore':
                        input_text = f"Given: {premise.strip()}"
                        target_text = f"Therefore: {conclusion.strip()}"
                    elif pattern_type == 'if_then':
                        input_text = f"If {premise.strip()}"
                        target_text = f"then {conclusion.strip()}"
                    elif pattern_type == 'if_then_not':
                        input_text = f"If {premise.strip()}"
                        target_text = f"then NOT {conclusion.strip()}"
                    elif pattern_type == 'premise_contradicts':
                        input_text = f"Premise: {premise.strip()}"
                        target_text = f"Contradicts: {conclusion.strip()}"
                    
                    examples.append((input_text, target_text))
                    
                    if len(examples) >= self.config['max_examples']//2:
                        break
                        
                if len(examples) >= self.config['max_examples']//2:
                    break
                    
        return examples
        
    def _load_all_data(self):
        """Load both types of data for final GAN training."""
        raw_examples = self._load_raw_text_data()
        reasoning_examples = self._load_reasoning_data()
        return raw_examples + reasoning_examples
        
    def _build_vocabulary(self, examples):
        """Build vocabulary from examples."""
        print("📚 Building vocabulary...")
        
        # Collect all text
        all_text = []
        for input_text, target_text in examples:
            all_text.extend(input_text.split())
            all_text.extend(target_text.split())
        
        # Count frequencies
        word_counts = Counter(all_text)
        
        # Create vocabulary
        vocab = ['<PAD>', '<UNK>', '<BOS>', '<EOS>']
        vocab.extend([word for word, count in word_counts.most_common(self.config['vocab_size'] - 4)])
        
        # Initialize tokenizer
        self.tokenizer = Tokenizer()
        vocab_data = {
            'vocab': {word: i for i, word in enumerate(vocab)},
            'id_to_token': {str(i): word for i, word in enumerate(vocab)},
            'vocab_size': len(vocab),
            'special_tokens': {'PAD': 0, 'UNK': 1, 'BOS': 2, 'EOS': 3}
        }
        
        # Save vocabulary
        os.makedirs("data", exist_ok=True)
        with open("data/vocab.txt", 'w', encoding='utf-8') as f:
            json.dump(vocab_data, f, indent=2, ensure_ascii=False)
            
        self.tokenizer.load("data/vocab.txt")
        print(f"✅ Built vocabulary: {len(vocab)} tokens")
        
    def _train_base_model(self, examples):
        """Train base NLP model."""
        print("🎯 Training base NLP model...")
        
        # Initialize generator
        vocab_size = self.tokenizer.get_vocab_size()
        self.generator = Generator(
            vocab_size=vocab_size,
            embed_dim=self.config['embed_dim'],
            hidden_dim=self.config['hidden_dim'],
            num_layers=1
        )
        
        # Training loop (simplified MLE)
        # Note: Adam expects params, but we'll handle this differently
        loss_fn = CrossEntropyLoss()
        
        for epoch in range(self.config['epochs']):
            total_loss = 0
            random.shuffle(examples)
            
            for i, (input_text, target_text) in enumerate(examples):
                # Tokenize
                input_ids = self.tokenizer.encode(input_text, add_bos=True, add_eos=False, max_len=15)
                target_ids = self.tokenizer.encode(target_text, add_bos=False, add_eos=True, max_len=10)
                
                if not input_ids or not target_ids:
                    continue
                
                # Forward pass
                logits, _ = self.generator.forward(input_ids)
                
                # Compute loss
                loss = loss_fn.forward(logits, target_ids)
                total_loss += loss
                
                # Backward pass (simplified)
                self.generator.backward(loss_fn.backward())
                
                if i % 100 == 0:
                    print(f"  Epoch {epoch+1}, Batch {i}: Loss {loss:.4f}")
            
            avg_loss = total_loss / len(examples)
            print(f"✅ Epoch {epoch+1} Complete: Avg Loss {avg_loss:.4f}")
            
    def _fine_tune_reasoning(self, examples):
        """Fine-tune model on reasoning data."""
        print("🧠 Fine-tuning for reasoning...")
        
        # Use lower learning rate for fine-tuning
        loss_fn = CrossEntropyLoss()
        
        for epoch in range(self.config['epochs']//2):  # Fewer epochs for fine-tuning
            total_loss = 0
            random.shuffle(examples)
            
            for i, (input_text, target_text) in enumerate(examples):
                # Tokenize
                input_ids = self.tokenizer.encode(input_text, add_bos=True, add_eos=False, max_len=20)
                target_ids = self.tokenizer.encode(target_text, add_bos=False, add_eos=True, max_len=15)
                
                if not input_ids or not target_ids:
                    continue
                
                # Forward pass
                logits, _ = self.generator.forward(input_ids)
                
                # Compute loss
                loss = loss_fn.forward(logits, target_ids)
                total_loss += loss
                
                # Backward pass
                self.generator.backward(loss_fn.backward())
                
                if i % 50 == 0:
                    print(f"  Reasoning Epoch {epoch+1}, Batch {i}: Loss {loss:.4f}")
            
            avg_loss = total_loss / len(examples)
            print(f"✅ Reasoning Epoch {epoch+1} Complete: Avg Loss {avg_loss:.4f}")
            
    def _train_gan(self, examples):
        """Train with GAN for quality improvement."""
        print("🥊 GAN adversarial training...")
        
        # Initialize GAN trainer
        gan_trainer = ReinforcementTrainer(
            generator=self.generator,
            discriminator=self.discriminator,
            tokenizer=self.tokenizer
        )
        
        # Convert examples to proper format for GAN training
        train_data = []
        for input_text, target_text in examples:
            combined_text = f"{input_text} {target_text}"
            tokens = self.tokenizer.encode(combined_text, add_bos=True, add_eos=True, max_len=30)
            if tokens:
                train_data.append(tokens)

        # GAN adversarial training
        print(f"🥊 Starting adversarial training with {len(train_data)} sequences...")
        gan_trainer.train_adversarial(
            train_data=train_data,
            val_data=train_data[:100],  # Use subset for validation
            num_steps=self.config['epochs'] * 50,  # Fewer steps for demo
            save_dir="models_out"
        )

        print("✅ GAN training complete")


def main():
    """Main training function."""
    parser = argparse.ArgumentParser(description="Sequential NLP Reasoning Training")
    parser.add_argument("--stage", type=int, choices=[1, 2, 3], help="Training stage")
    parser.add_argument("--all", action="store_true", help="Run all stages")
    parser.add_argument("--config", type=str, default="balanced", help="Training config")
    
    args = parser.parse_args()
    
    # Training configuration
    config = {
        'max_examples': 1000,
        'vocab_size': 2000,
        'embed_dim': 64,
        'hidden_dim': 128,
        'epochs': 8,
        'learning_rate': 0.003
    }
    
    trainer = SequentialTrainer(config)
    
    if args.all:
        print("🚀 SEQUENTIAL TRAINING: All Stages")
        print("=" * 60)
        trainer.stage_1_base_nlp()
        trainer.stage_2_reasoning()
        trainer.stage_3_gan_refinement()
        print("\n🎉 ALL STAGES COMPLETE!")
        print("🤖 Ready to chat: python chat.py --model models_out/stage3_final.json")
        
    elif args.stage == 1:
        trainer.stage_1_base_nlp()
    elif args.stage == 2:
        trainer.stage_2_reasoning()
    elif args.stage == 3:
        trainer.stage_3_gan_refinement()
    else:
        print("❌ Please specify --stage 1, --stage 2, --stage 3, or --all")


if __name__ == "__main__":
    main()
