"""
UPDATED Data Preparation Script for NLP GAN Reasoner
Parses reasoning_raw.txt and raw_text.txt to produce clean, structured training data.

Usage:
  python scripts/prepare_data.py

Creates:
  - data/train_reasoning.txt (clean structured reasoning pairs)
  - data/val_reasoning.txt (validation data)
  - data/vocab.txt (vocabulary file)
"""
import sys
import os
import re
import random
import json
from collections import Counter

# Configuration
MAX_EXAMPLES = 2000
VOCAB_SIZE = 3000
VAL_FRACTION = 0.1


def clean_text(text):
    """Clean and normalize text."""
    if not text:
        return ""

    # Remove extra whitespace and normalize
    text = re.sub(r'\s+', ' ', text.strip())

    # Remove problematic characters but keep basic punctuation
    text = re.sub(r'[^\w\s.,!?;:()\-\'\"]+', '', text)

    return text


def parse_reasoning_patterns(content):
    """Parse structured reasoning patterns from reasoning_raw.txt."""
    pairs = []

    # Define patterns to extract
    patterns = [
        (r'Given: (.+?)\. Therefore: (.+?)\.', 'given_therefore'),
        (r'If (.+?)\., then (.+?)\.', 'if_then'),
        (r'If (.+?)\., then NOT \((.+?)\.\)', 'if_then_not'),
        (r'Premise: (.+?)\. Contradicts: (.+?)\.', 'premise_contradicts'),
        (r'Given: (.+?)\. Possibly: (.+?)\.', 'given_possibly'),
        (r"From '(.+?)' we cannot conclude '(.+?)'", 'cannot_conclude'),
    ]

    for pattern, pattern_type in patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)

        for match in matches:
            premise, conclusion = match

            # Create structured reasoning pairs
            if pattern_type == 'given_therefore':
                input_text = f"Given: {clean_text(premise)}"
                target_text = f"Therefore: {clean_text(conclusion)}"
            elif pattern_type == 'if_then':
                input_text = f"If {clean_text(premise)}"
                target_text = f"then {clean_text(conclusion)}"
            elif pattern_type == 'if_then_not':
                input_text = f"If {clean_text(premise)}"
                target_text = f"then NOT {clean_text(conclusion)}"
            elif pattern_type == 'premise_contradicts':
                input_text = f"Premise: {clean_text(premise)}"
                target_text = f"Contradicts: {clean_text(conclusion)}"
            elif pattern_type == 'given_possibly':
                input_text = f"Given: {clean_text(premise)}"
                target_text = f"Possibly: {clean_text(conclusion)}"
            elif pattern_type == 'cannot_conclude':
                input_text = f"From {clean_text(premise)}"
                target_text = f"we cannot conclude {clean_text(conclusion)}"

            pairs.append((input_text, target_text))

    return pairs


def process_raw_text(file_path):
    """Process raw_text.txt for general language modeling."""
    pairs = []

    if not os.path.exists(file_path):
        print(f"Raw text file not found: {file_path}")
        return pairs

    print(f"Processing raw text file: {file_path}")

    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
        lines = f.readlines()

    for line in lines:
        line = clean_text(line)
        if len(line) > 10 and len(line) < 150:
            words = line.split()
            if len(words) >= 4:
                # Create input-target pairs for language modeling
                for i in range(2, min(len(words), 8)):
                    input_text = ' '.join(words[:i])
                    target_text = words[i]
                    pairs.append((input_text, target_text))

    return pairs


def split_and_write(pairs, val_frac=0.1):
    """Split data pairs into train/val and write to files."""
    if not pairs:
        print("No data pairs to split!")
        return

    # Shuffle data
    random.seed(42)
    random.shuffle(pairs)

    # Calculate split indices
    total = len(pairs)
    val_size = int(total * val_frac)

    # Split data
    val_pairs = pairs[:val_size]
    train_pairs = pairs[val_size:]

    print(f"Data split: {len(train_pairs)} train, {len(val_pairs)} validation")

    # Ensure data directory exists
    os.makedirs("data", exist_ok=True)

    # Write training data
    with open("data/train_reasoning.txt", 'w', encoding='utf-8') as f:
        for input_str, target_str in train_pairs:
            f.write(f"{input_str}\t{target_str}\n")

    # Write validation data
    with open("data/val_reasoning.txt", 'w', encoding='utf-8') as f:
        for input_str, target_str in val_pairs:
            f.write(f"{input_str}\t{target_str}\n")

    print(f"Written {len(train_pairs)} training examples to data/train_reasoning.txt")
    print(f"Written {len(val_pairs)} validation examples to data/val_reasoning.txt")


def main():
    """Main data preparation function."""
    print("🚀 UPDATED Data Preparation for NLP GAN Reasoner")
    print("=" * 60)

    # Process raw text
    raw_text_pairs = []
    if os.path.exists("data/raw_text.txt"):
        raw_text_pairs = process_raw_text("data/raw_text.txt")
        print(f"✅ Processed {len(raw_text_pairs)} raw text pairs")

    # Process reasoning data
    reasoning_pairs = []
    if os.path.exists("data/reasoning_raw.txt"):
        print("📖 Processing reasoning_raw.txt...")
        with open("data/reasoning_raw.txt", 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        reasoning_pairs = parse_reasoning_patterns(content)
        print(f"✅ Processed {len(reasoning_pairs)} reasoning pairs")

    # Combine all data
    all_pairs = raw_text_pairs + reasoning_pairs

    # Limit total examples
    if len(all_pairs) > MAX_EXAMPLES:
        random.seed(42)
        random.shuffle(all_pairs)
        all_pairs = all_pairs[:MAX_EXAMPLES]

    print(f"\n📊 SUMMARY:")
    print(f"  📝 Raw text pairs: {len(raw_text_pairs)}")
    print(f"  🧠 Reasoning pairs: {len(reasoning_pairs)}")
    print(f"  📦 Total pairs: {len(all_pairs)}")

    # Show sample data
    print(f"\n🔍 SAMPLE DATA:")
    for i, (input_text, target_text) in enumerate(all_pairs[:3]):
        print(f"  Example {i+1}:")
        print(f"    Input: {input_text}")
        print(f"    Target: {target_text}")

    # Split and write
    split_and_write(all_pairs, VAL_FRACTION)

    # Build vocabulary
    print(f"\n📚 Building vocabulary...")
    all_text = []
    for input_text, target_text in all_pairs:
        all_text.extend(input_text.split())
        all_text.extend(target_text.split())

    # Count word frequencies
    word_counts = Counter(all_text)

    # Create vocabulary (most frequent words)
    vocab = ['<PAD>', '<UNK>', '<BOS>', '<EOS>']  # Special tokens
    vocab.extend([word for word, count in word_counts.most_common(VOCAB_SIZE - 4)])

    # Save vocabulary in JSON format (for tokenizer compatibility)
    special_tokens = {
        'PAD': 0,
        'UNK': 1,
        'BOS': 2,
        'EOS': 3
    }

    vocab_data = {
        'vocab': {word: i for i, word in enumerate(vocab)},
        'id_to_token': {str(i): word for i, word in enumerate(vocab)},
        'vocab_size': len(vocab),
        'special_tokens': special_tokens
    }

    with open("data/vocab.txt", 'w', encoding='utf-8') as f:
        json.dump(vocab_data, f, indent=2, ensure_ascii=False)

    print(f"✅ Built vocabulary: {len(vocab)} tokens")
    print(f"💾 Saved: data/vocab.txt")

    print("\n" + "=" * 60)
    print("🎉 Data preparation completed successfully!")
    print("📁 Created files:")
    print("  - data/train_reasoning.txt")
    print("  - data/val_reasoning.txt")
    print("  - data/vocab.txt")
    print("\n🚀 Ready to train: python train.py --level balanced")


if __name__ == "__main__":
    main()