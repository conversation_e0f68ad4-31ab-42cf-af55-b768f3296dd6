import re

def clean_reasoning_file(input_filepath, output_filepath):
    """
    Reads a raw reasoning dataset from a file, cleans it, and writes it to a new file.

    Args:
        input_filepath (str): The path to the raw input file.
        output_filepath (str): The path where the cleaned output file will be saved.
    """
    cleaned_lines = []
    
    try:
        with open(input_filepath, 'r', encoding='utf-8') as f:
            for line in f:
                # Strip leading/trailing whitespace
                line = line.strip()
                
                # Skip empty lines
                if not line:
                    continue
                
                # Clean up empty marks and multiple spaces
                line = re.sub(r' \. ', ' ', line)
                line = re.sub(r' , ', ' ', line)
                line = re.sub(r'\s+', ' ', line)
                line = line.strip()
                
                # Check if the line is not just a single punctuation mark after cleaning
                if line and line not in ['.', ',', '?', '!']:
                    cleaned_lines.append(line)
        
        with open(output_filepath, 'w', encoding='utf-8') as f:
            f.write('\n'.join(cleaned_lines))
        
        print(f"✅ Cleaned data successfully saved to {output_filepath}")
        
    except FileNotFoundError:
        print(f"Error: The file '{input_filepath}' was not found.")
    except Exception as e:
        print(f"An error occurred: {e}")

# --- Usage Example ---
# Assuming your raw data is in a file named 'raw_reasoning.txt'
input_file = 'reasoning_raw.txt'
output_file = 'cleaned_reasoning.txt'

# Call the function to clean the file
clean_reasoning_file(input_file, output_file)
