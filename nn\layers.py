"""
Neural network layers with forward/backward passes.
Implements <PERSON><PERSON>, <PERSON><PERSON><PERSON>, G<PERSON><PERSON>ell, CrossEntropyLoss.
"""
import sys
import os
import math
import random

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.math_ops import *
import config


class Dense:
    """Fully connected (dense) layer."""

    def __init__(self, in_dim, out_dim, use_bias=True):
        """
        Initialize dense layer.

        Args:
            in_dim: Input dimension
            out_dim: Output dimension
            use_bias: Whether to use bias term
        """
        self.in_dim = in_dim
        self.out_dim = out_dim
        self.use_bias = use_bias

        # Initialize weights with Xavier/Glorot initialization
        std = math.sqrt(2.0 / (in_dim + out_dim))
        self.W = random_gauss((in_dim, out_dim), 0.0, std)

        if use_bias:
            self.b = zeros(out_dim)
        else:
            self.b = None

        # Gradients
        self.dW = zeros((in_dim, out_dim))
        if use_bias:
            self.db = zeros(out_dim)
        else:
            self.db = None

        # Cache for backward pass
        self.cache = {}

    def forward(self, x):
        """
        Forward pass.

        Args:
            x: Input, can be:
               - Single vector (length in_dim)
               - Batch of vectors (list of vectors)
               - Sequence of vectors (list of vectors for sequence)

        Returns:
            Output with same structure as input
        """
        # Handle different input types
        if isinstance(x[0], list):
            # Batch or sequence of vectors
            outputs = []
            for vec in x:
                if len(vec) != self.in_dim:
                    raise ValueError(f"Input dimension {len(vec)} doesn't match layer input dim {self.in_dim}")

                # Compute y = x @ W + b
                y = matvec(transpose(self.W), vec)  # W^T @ x
                if self.use_bias:
                    y = add(y, self.b)
                outputs.append(y)

            # Cache for backward pass
            self.cache['input'] = x
            self.cache['output'] = outputs
            return outputs
        else:
            # Single vector
            if len(x) != self.in_dim:
                raise ValueError(f"Input dimension {len(x)} doesn't match layer input dim {self.in_dim}")

            # Compute y = x @ W + b
            y = matvec(transpose(self.W), x)  # W^T @ x
            if self.use_bias:
                y = add(y, self.b)

            # Cache for backward pass
            self.cache['input'] = x
            self.cache['output'] = y
            return y

    def backward(self, dout):
        """
        Backward pass.

        Args:
            dout: Gradient w.r.t. output (same structure as forward output)

        Returns:
            Gradient w.r.t. input (same structure as forward input)
        """
        x = self.cache['input']

        # Reset gradients
        self.dW = zeros((self.in_dim, self.out_dim))
        if self.use_bias:
            self.db = zeros(self.out_dim)

        if isinstance(x[0], list):
            # Batch or sequence
            dx = []
            for i, (x_i, dout_i) in enumerate(zip(x, dout)):
                # Gradient w.r.t. input: dx = W @ dout
                dx_i = matvec(self.W, dout_i)
                dx.append(dx_i)

                # Accumulate gradients w.r.t. parameters
                # dW += x_i^T @ dout_i (outer product)
                for j in range(self.in_dim):
                    for k in range(self.out_dim):
                        self.dW[j][k] += x_i[j] * dout_i[k]

                # db += dout_i
                if self.use_bias:
                    self.db = add(self.db, dout_i)

            return dx
        else:
            # Single vector
            # Gradient w.r.t. input: dx = W @ dout
            dx = matvec(self.W, dout)

            # Gradients w.r.t. parameters
            # dW = x^T @ dout (outer product)
            for i in range(self.in_dim):
                for j in range(self.out_dim):
                    self.dW[i][j] = x[i] * dout[j]

            # db = dout
            if self.use_bias:
                self.db = dout[:]

            return dx

    def get_params(self):
        """Get parameters as list."""
        params = [self.W]
        if self.use_bias:
            params.append(self.b)
        return params

    def get_grads(self):
        """Get gradients as list."""
        grads = [self.dW]
        if self.use_bias:
            grads.append(self.db)
        return grads


class Embedding:
    """Embedding layer for token IDs."""

    def __init__(self, vocab_size, embed_dim):
        """
        Initialize embedding layer.

        Args:
            vocab_size: Size of vocabulary
            embed_dim: Embedding dimension
        """
        self.vocab_size = vocab_size
        self.embed_dim = embed_dim

        # Initialize embedding matrix
        std = math.sqrt(1.0 / embed_dim)
        self.W = random_gauss((vocab_size, embed_dim), 0.0, std)

        # Gradients (sparse updates)
        self.dW = zeros((vocab_size, embed_dim))

        # Cache for backward pass
        self.cache = {}

    def forward(self, ids):
        """
        Forward pass.

        Args:
            ids: Token IDs, can be:
                 - Single ID (int)
                 - List of IDs (sequence)
                 - List of lists of IDs (batch of sequences)

        Returns:
            Embeddings with corresponding structure
        """
        if isinstance(ids, int):
            # Single ID
            if ids >= self.vocab_size or ids < 0:
                ids = 1  # UNK token

            embedding = self.W[ids][:]  # Copy the row
            self.cache['ids'] = ids
            self.cache['output'] = embedding
            return embedding

        elif isinstance(ids[0], int):
            # Sequence of IDs
            embeddings = []
            for token_id in ids:
                if token_id >= self.vocab_size or token_id < 0:
                    token_id = 1  # UNK token
                embeddings.append(self.W[token_id][:])  # Copy the row

            self.cache['ids'] = ids
            self.cache['output'] = embeddings
            return embeddings

        else:
            # Batch of sequences
            batch_embeddings = []
            for seq_ids in ids:
                seq_embeddings = []
                for token_id in seq_ids:
                    if token_id >= self.vocab_size or token_id < 0:
                        token_id = 1  # UNK token
                    seq_embeddings.append(self.W[token_id][:])
                batch_embeddings.append(seq_embeddings)

            self.cache['ids'] = ids
            self.cache['output'] = batch_embeddings
            return batch_embeddings

    def backward(self, dout):
        """
        Backward pass.

        Args:
            dout: Gradient w.r.t. output embeddings

        Returns:
            None (no gradient w.r.t. input IDs)
        """
        ids = self.cache['ids']

        # Reset gradients
        self.dW = zeros((self.vocab_size, self.embed_dim))

        if isinstance(ids, int):
            # Single ID
            if ids < self.vocab_size and ids >= 0:
                for j in range(self.embed_dim):
                    self.dW[ids][j] += dout[j]

        elif isinstance(ids[0], int):
            # Sequence of IDs
            for i, token_id in enumerate(ids):
                if token_id < self.vocab_size and token_id >= 0:
                    for j in range(self.embed_dim):
                        self.dW[token_id][j] += dout[i][j]

        else:
            # Batch of sequences
            for seq_idx, seq_ids in enumerate(ids):
                for token_idx, token_id in enumerate(seq_ids):
                    if token_id < self.vocab_size and token_id >= 0:
                        for j in range(self.embed_dim):
                            self.dW[token_id][j] += dout[seq_idx][token_idx][j]

        return None  # No gradient w.r.t. input IDs

    def get_params(self):
        """Get parameters as list."""
        return [self.W]

    def get_grads(self):
        """Get gradients as list."""
        return [self.dW]


class GRUCell:
    """Single GRU cell implementation."""

    def __init__(self, input_dim, hidden_dim):
        """
        Initialize GRU cell.

        Args:
            input_dim: Input dimension
            hidden_dim: Hidden state dimension
        """
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim

        # Initialize weights
        std = math.sqrt(1.0 / hidden_dim)

        # Update gate weights
        self.Wz = random_gauss((input_dim, hidden_dim), 0.0, std)
        self.Uz = random_gauss((hidden_dim, hidden_dim), 0.0, std)
        self.bz = zeros(hidden_dim)

        # Reset gate weights
        self.Wr = random_gauss((input_dim, hidden_dim), 0.0, std)
        self.Ur = random_gauss((hidden_dim, hidden_dim), 0.0, std)
        self.br = zeros(hidden_dim)

        # Candidate hidden state weights
        self.Wh = random_gauss((input_dim, hidden_dim), 0.0, std)
        self.Uh = random_gauss((hidden_dim, hidden_dim), 0.0, std)
        self.bh = zeros(hidden_dim)

        # Gradients
        self.dWz = zeros((input_dim, hidden_dim))
        self.dUz = zeros((hidden_dim, hidden_dim))
        self.dbz = zeros(hidden_dim)

        self.dWr = zeros((input_dim, hidden_dim))
        self.dUr = zeros((hidden_dim, hidden_dim))
        self.dbr = zeros(hidden_dim)

        self.dWh = zeros((input_dim, hidden_dim))
        self.dUh = zeros((hidden_dim, hidden_dim))
        self.dbh = zeros(hidden_dim)

        # Cache for backward pass
        self.cache = {}

    def sigmoid(self, x):
        """Sigmoid activation function."""
        if isinstance(x[0], list):
            # Matrix
            result = []
            for row in x:
                result.append([1.0 / (1.0 + exp(-val)) for val in row])
            return result
        else:
            # Vector
            return [1.0 / (1.0 + exp(-val)) for val in x]

    def tanh(self, x):
        """Tanh activation function."""
        if isinstance(x[0], list):
            # Matrix
            result = []
            for row in x:
                result.append([(exp(val) - exp(-val)) / (exp(val) + exp(-val)) for val in row])
            return result
        else:
            # Vector
            return [(exp(val) - exp(-val)) / (exp(val) + exp(-val)) for val in x]

    def forward(self, x, h_prev):
        """
        Forward pass for single time step.

        Args:
            x: Input vector (input_dim,)
            h_prev: Previous hidden state (hidden_dim,)

        Returns:
            h: New hidden state (hidden_dim,)
        """
        # Update gate: z = sigmoid(x @ Wz + h_prev @ Uz + bz)
        z_input = add(add(matvec(transpose(self.Wz), x), matvec(transpose(self.Uz), h_prev)), self.bz)
        z = self.sigmoid(z_input)

        # Reset gate: r = sigmoid(x @ Wr + h_prev @ Ur + br)
        r_input = add(add(matvec(transpose(self.Wr), x), matvec(transpose(self.Ur), h_prev)), self.br)
        r = self.sigmoid(r_input)

        # Candidate hidden state: h_hat = tanh(x @ Wh + (r * h_prev) @ Uh + bh)
        r_h_prev = elementwise_mul(r, h_prev)
        h_hat_input = add(add(matvec(transpose(self.Wh), x), matvec(transpose(self.Uh), r_h_prev)), self.bh)
        h_hat = self.tanh(h_hat_input)

        # New hidden state: h = (1 - z) * h_prev + z * h_hat
        one_minus_z = [1.0 - z_i for z_i in z]
        h = add(elementwise_mul(one_minus_z, h_prev), elementwise_mul(z, h_hat))

        # Cache for backward pass
        self.cache = {
            'x': x,
            'h_prev': h_prev,
            'z': z,
            'r': r,
            'h_hat': h_hat,
            'r_h_prev': r_h_prev,
            'z_input': z_input,
            'r_input': r_input,
            'h_hat_input': h_hat_input
        }

        return h

    def backward(self, dh):
        """
        Backward pass for single time step.

        Args:
            dh: Gradient w.r.t. output hidden state

        Returns:
            dx: Gradient w.r.t. input
            dh_prev: Gradient w.r.t. previous hidden state
        """
        # Get cached values
        x = self.cache['x']
        h_prev = self.cache['h_prev']
        z = self.cache['z']
        r = self.cache['r']
        h_hat = self.cache['h_hat']
        r_h_prev = self.cache['r_h_prev']

        # Reset gradients
        self.dWz = zeros((self.input_dim, self.hidden_dim))
        self.dUz = zeros((self.hidden_dim, self.hidden_dim))
        self.dbz = zeros(self.hidden_dim)

        self.dWr = zeros((self.input_dim, self.hidden_dim))
        self.dUr = zeros((self.hidden_dim, self.hidden_dim))
        self.dbr = zeros(self.hidden_dim)

        self.dWh = zeros((self.input_dim, self.hidden_dim))
        self.dUh = zeros((self.hidden_dim, self.hidden_dim))
        self.dbh = zeros(self.hidden_dim)

        # Gradient w.r.t. h = (1 - z) * h_prev + z * h_hat
        # dh/dz = -h_prev + h_hat
        # dh/dh_prev = (1 - z)
        # dh/dh_hat = z

        dz = elementwise_mul(dh, sub(h_hat, h_prev))
        dh_prev_direct = elementwise_mul(dh, [1.0 - z_i for z_i in z])
        dh_hat = elementwise_mul(dh, z)

        # Gradient w.r.t. h_hat = tanh(h_hat_input)
        # dtanh/dx = 1 - tanh^2(x)
        tanh_grad = [1.0 - h_hat_i * h_hat_i for h_hat_i in h_hat]
        dh_hat_input = elementwise_mul(dh_hat, tanh_grad)

        # Gradient w.r.t. h_hat_input = x @ Wh + r_h_prev @ Uh + bh
        dx_from_h_hat = matvec(self.Wh, dh_hat_input)
        dr_h_prev = matvec(self.Uh, dh_hat_input)

        # Accumulate parameter gradients for h_hat
        for i in range(self.input_dim):
            for j in range(self.hidden_dim):
                self.dWh[i][j] += x[i] * dh_hat_input[j]

        for i in range(self.hidden_dim):
            for j in range(self.hidden_dim):
                self.dUh[i][j] += r_h_prev[i] * dh_hat_input[j]

        self.dbh = dh_hat_input[:]

        # Gradient w.r.t. r_h_prev = r * h_prev
        dr = elementwise_mul(dr_h_prev, h_prev)
        dh_prev_from_r = elementwise_mul(dr_h_prev, r)

        # Gradient w.r.t. r = sigmoid(r_input)
        # dsigmoid/dx = sigmoid(x) * (1 - sigmoid(x))
        sigmoid_grad_r = [r_i * (1.0 - r_i) for r_i in r]
        dr_input = elementwise_mul(dr, sigmoid_grad_r)

        # Gradient w.r.t. r_input = x @ Wr + h_prev @ Ur + br
        dx_from_r = matvec(self.Wr, dr_input)
        dh_prev_from_r_input = matvec(self.Ur, dr_input)

        # Accumulate parameter gradients for r
        for i in range(self.input_dim):
            for j in range(self.hidden_dim):
                self.dWr[i][j] += x[i] * dr_input[j]

        for i in range(self.hidden_dim):
            for j in range(self.hidden_dim):
                self.dUr[i][j] += h_prev[i] * dr_input[j]

        self.dbr = dr_input[:]

        # Gradient w.r.t. z = sigmoid(z_input)
        sigmoid_grad_z = [z_i * (1.0 - z_i) for z_i in z]
        dz_input = elementwise_mul(dz, sigmoid_grad_z)

        # Gradient w.r.t. z_input = x @ Wz + h_prev @ Uz + bz
        dx_from_z = matvec(self.Wz, dz_input)
        dh_prev_from_z_input = matvec(self.Uz, dz_input)

        # Accumulate parameter gradients for z
        for i in range(self.input_dim):
            for j in range(self.hidden_dim):
                self.dWz[i][j] += x[i] * dz_input[j]

        for i in range(self.hidden_dim):
            for j in range(self.hidden_dim):
                self.dUz[i][j] += h_prev[i] * dz_input[j]

        self.dbz = dz_input[:]

        # Combine gradients
        dx = add(add(dx_from_h_hat, dx_from_r), dx_from_z)
        dh_prev = add(add(add(dh_prev_direct, dh_prev_from_r), dh_prev_from_r_input), dh_prev_from_z_input)

        return dx, dh_prev

    def get_params(self):
        """Get parameters as list."""
        return [self.Wz, self.Uz, self.bz, self.Wr, self.Ur, self.br, self.Wh, self.Uh, self.bh]

    def get_grads(self):
        """Get gradients as list."""
        return [self.dWz, self.dUz, self.dbz, self.dWr, self.dUr, self.dbr, self.dWh, self.dUh, self.dbh]


class CrossEntropyLoss:
    """Cross-entropy loss for classification."""

    def __init__(self):
        """Initialize cross-entropy loss."""
        self.cache = {}

    def forward(self, logits, target_ids, mask=None):
        """
        Forward pass.

        Args:
            logits: Predicted logits (batch_size, seq_len, vocab_size) or (seq_len, vocab_size)
            target_ids: Target token IDs (batch_size, seq_len) or (seq_len,)
            mask: Optional mask for padding (same shape as target_ids)

        Returns:
            Scalar loss value
        """
        # Handle single sequence case
        if isinstance(target_ids[0], int):
            logits = [logits]
            target_ids = [target_ids]
            if mask is not None:
                mask = [mask]

        total_loss = 0.0
        total_count = 0
        probs_list = []

        for batch_idx, (seq_logits, seq_targets) in enumerate(zip(logits, target_ids)):
            seq_probs = []
            seq_mask = mask[batch_idx] if mask else None

            for t, (step_logits, target_id) in enumerate(zip(seq_logits, seq_targets)):
                # Skip if masked
                if seq_mask and seq_mask[t] == 0:
                    seq_probs.append(zeros(len(step_logits)))
                    continue

                # Compute softmax probabilities
                probs = softmax_row(step_logits)
                seq_probs.append(probs)

                # Compute cross-entropy loss: -log(p[target])
                if target_id < len(probs) and target_id >= 0:
                    loss = -log(max(probs[target_id], 1e-10))  # Avoid log(0)
                    total_loss += loss
                    total_count += 1

            probs_list.append(seq_probs)

        # Average loss
        avg_loss = total_loss / max(total_count, 1)

        # Cache for backward pass
        self.cache = {
            'probs': probs_list,
            'target_ids': target_ids,
            'mask': mask,
            'total_count': total_count
        }

        return avg_loss

    def backward(self):
        """
        Backward pass.

        Returns:
            Gradient w.r.t. logits (same shape as input logits)
        """
        probs_list = self.cache['probs']
        target_ids = self.cache['target_ids']
        mask = self.cache['mask']
        total_count = self.cache['total_count']

        dlogits = []

        for batch_idx, (seq_probs, seq_targets) in enumerate(zip(probs_list, target_ids)):
            seq_dlogits = []
            seq_mask = mask[batch_idx] if mask else None

            for t, (probs, target_id) in enumerate(zip(seq_probs, seq_targets)):
                # Skip if masked
                if seq_mask and seq_mask[t] == 0:
                    seq_dlogits.append(zeros(len(probs)))
                    continue

                # Gradient: probs - one_hot(target)
                dlogits_t = probs[:]
                if target_id < len(probs) and target_id >= 0:
                    dlogits_t[target_id] -= 1.0

                # Scale by 1/total_count for averaging
                dlogits_t = scalar_mul(dlogits_t, 1.0 / max(total_count, 1))
                seq_dlogits.append(dlogits_t)

            dlogits.append(seq_dlogits)

        # Return single sequence if input was single sequence
        if len(dlogits) == 1:
            return dlogits[0]

        return dlogits


def clip_gradients(params, grads, max_norm):
    """
    Clip gradients by global norm.

    Args:
        params: List of parameter arrays
        grads: List of gradient arrays (same structure as params)
        max_norm: Maximum gradient norm

    Returns:
        Clipped gradients
    """
    # Compute global norm
    total_norm_sq = 0.0

    for grad in grads:
        if isinstance(grad[0], list):
            # Matrix
            for row in grad:
                for val in row:
                    total_norm_sq += val * val
        else:
            # Vector
            for val in grad:
                total_norm_sq += val * val

    total_norm = math.sqrt(total_norm_sq)

    # Clip if necessary
    if total_norm > max_norm:
        clip_factor = max_norm / total_norm

        clipped_grads = []
        for grad in grads:
            if isinstance(grad[0], list):
                # Matrix
                clipped_grad = []
                for row in grad:
                    clipped_row = [val * clip_factor for val in row]
                    clipped_grad.append(clipped_row)
                clipped_grads.append(clipped_grad)
            else:
                # Vector
                clipped_grad = [val * clip_factor for val in grad]
                clipped_grads.append(clipped_grad)

        return clipped_grads

    return grads
