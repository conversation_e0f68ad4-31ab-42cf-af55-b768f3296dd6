"""
IMPROVED SEQUENTIAL NLP GAN REASONER
Implements the correct 4-stage training pipeline with proper separation.

Architecture:
1. Stage 1: Base Language Model (foundation from raw_text.txt)
2. Stage 2: Reasoning Fine-tuning (logical capabilities from reasoning_raw.txt)  
3. Stage 3: GAN Refinement (quality improvement through adversarial training)
4. Stage 4: Chat Interface (intelligent model selection)

Usage:
  python train_improved.py --stage base      # Train base language model
  python train_improved.py --stage reasoning # Fine-tune for reasoning
  python train_improved.py --stage gan       # GAN quality refinement
  python train_improved.py --full            # Run all stages
  python train_improved.py --chat            # Launch chat interface
"""
import sys
import os
import time
import json
import random
import argparse
import re
from collections import defaultdict, Counter
from typing import List, Tuple, Dict, Optional

# Add current directory to path
sys.path.append('.')

from models.generator import Generator
from models.discriminator import Discriminator
from nlp.tokenizer import Tokenizer
from nn.optim import Adam
from nn.layers import CrossEntropyLoss
from gan.trainer import pretrain_generator_mle, pretrain_discriminator, ReinforcementTrainer
import config


class ImprovedSequentialTrainer:
    """Improved sequential trainer with proper stage separation."""
    
    def __init__(self, training_config):
        self.config = training_config
        self.tokenizer = None
        self.generator = None
        self.discriminator = None
        
        # Ensure output directory exists
        os.makedirs("models_out", exist_ok=True)
        os.makedirs("data", exist_ok=True)
    
    def stage_1_base_language(self):
        """Stage 1: Train base language model on general text only."""
        print("🧠 STAGE 1: BASE LANGUAGE MODEL TRAINING")
        print("=" * 70)
        print("📚 Training on general language patterns from raw_text.txt")
        print("🎯 Goal: Learn vocabulary, grammar, and basic language structure")
        print()
        
        # Load ONLY general language data
        base_examples = self._load_base_language_data()
        print(f"📖 Loaded {len(base_examples)} base language examples")
        
        # Build vocabulary from base language
        self.tokenizer = self._build_vocabulary(base_examples, "base_vocab")
        
        # Convert to sequences
        base_sequences = self._prepare_sequences(base_examples, "base language")
        
        # Create and train base model
        self.generator = Generator(
            vocab_size=self.tokenizer.get_vocab_size(),
            embed_dim=self.config['embed_dim'],
            hidden_dim=self.config['hidden_dim'],
            num_layers=self.config['num_layers']
        )
        
        # Train base language model
        self._train_language_model(base_sequences, "Stage 1 - Base Language")
        
        # Save stage 1 model
        stage1_path = "models_out/stage1_base_language.json"
        self.generator.save(stage1_path)
        self.tokenizer.save("data/stage1_vocab.txt")
        
        print(f"✅ STAGE 1 COMPLETE")
        print(f"💾 Base language model saved: {stage1_path}")
        print(f"📚 Vocabulary saved: data/stage1_vocab.txt")
        print()
        
        return self.generator
    
    def stage_2_reasoning_finetuning(self):
        """Stage 2: Fine-tune base model for reasoning capabilities."""
        print("🔗 STAGE 2: REASONING FINE-TUNING")
        print("=" * 70)
        print("📚 Fine-tuning on structured reasoning patterns from reasoning_raw.txt")
        print("🎯 Goal: Add logical reasoning (Given/Therefore, If/Then, etc.)")
        print()
        
        # Load stage 1 model
        stage1_path = "models_out/stage1_base_language.json"
        if not os.path.exists(stage1_path):
            print("❌ Stage 1 model not found! Run --stage base first.")
            return None
            
        # Load base model and vocabulary
        self.tokenizer = Tokenizer()
        self.tokenizer.load("data/stage1_vocab.txt")
        
        self.generator = Generator(
            vocab_size=self.tokenizer.get_vocab_size(),
            embed_dim=self.config['embed_dim'],
            hidden_dim=self.config['hidden_dim'],
            num_layers=self.config['num_layers']
        )
        self.generator.load(stage1_path)
        print(f"✅ Loaded Stage 1 base model from {stage1_path}")
        
        # Load ONLY reasoning data
        reasoning_examples = self._load_reasoning_data()
        print(f"🧠 Loaded {len(reasoning_examples)} reasoning examples")
        
        # Convert to sequences (extend vocabulary if needed)
        reasoning_sequences = self._prepare_sequences(reasoning_examples, "reasoning", extend_vocab=True)
        
        # Fine-tune for reasoning with lower learning rate
        self._fine_tune_reasoning(reasoning_sequences)
        
        # Save stage 2 model
        stage2_path = "models_out/stage2_reasoning.json"
        self.generator.save(stage2_path)
        self.tokenizer.save("data/stage2_vocab.txt")
        
        print(f"✅ STAGE 2 COMPLETE")
        print(f"💾 Reasoning model saved: {stage2_path}")
        print(f"📚 Extended vocabulary saved: data/stage2_vocab.txt")
        print()
        
        return self.generator
    
    def stage_3_gan_refinement(self):
        """Stage 3: GAN refinement for quality improvement."""
        print("🎭 STAGE 3: GAN QUALITY REFINEMENT")
        print("=" * 70)
        print("📚 Adversarial training for quality improvement")
        print("🎯 Goal: Polish reasoning quality through generator-discriminator competition")
        print()
        
        # Load stage 2 model
        stage2_path = "models_out/stage2_reasoning.json"
        if not os.path.exists(stage2_path):
            print("❌ Stage 2 model not found! Run --stage reasoning first.")
            return None
            
        # Load reasoning model and vocabulary
        self.tokenizer = Tokenizer()
        self.tokenizer.load("data/stage2_vocab.txt")
        
        self.generator = Generator(
            vocab_size=self.tokenizer.get_vocab_size(),
            embed_dim=self.config['embed_dim'],
            hidden_dim=self.config['hidden_dim'],
            num_layers=self.config['num_layers']
        )
        self.generator.load(stage2_path)
        print(f"✅ Loaded Stage 2 reasoning model from {stage2_path}")
        
        # Create discriminator
        self.discriminator = Discriminator(vocab_size=self.tokenizer.get_vocab_size())
        print("✅ Created discriminator model")
        
        # Load combined data for GAN training
        combined_data = self._load_combined_data_for_gan()
        print(f"📚 Loaded {len(combined_data)} examples for GAN training")
        
        # Run GAN training pipeline
        self._train_gan_pipeline(combined_data)
        
        # Save final models
        stage3_gen_path = "models_out/stage3_final_generator.json"
        stage3_dis_path = "models_out/stage3_final_discriminator.json"
        
        self.generator.save(stage3_gen_path)
        self.discriminator.save(stage3_dis_path)
        
        print(f"✅ STAGE 3 COMPLETE")
        print(f"💾 Final generator saved: {stage3_gen_path}")
        print(f"💾 Final discriminator saved: {stage3_dis_path}")
        print()
        
        return self.generator, self.discriminator
    
    def stage_4_chat_interface(self):
        """Stage 4: Launch intelligent chat interface."""
        print("💬 STAGE 4: INTELLIGENT CHAT INTERFACE")
        print("=" * 70)
        print("🤖 Launching chat with best available model")
        print()
        
        # Import and launch chat
        from chat import UnifiedChatAgent
        
        # Auto-detect best model
        if os.path.exists("models_out/stage3_final_generator.json"):
            model_path = "models_out/stage3_final_generator.json"
            vocab_path = "data/stage2_vocab.txt"
            print("🎭 Using Stage 3 GAN-refined model")
        elif os.path.exists("models_out/stage2_reasoning.json"):
            model_path = "models_out/stage2_reasoning.json"
            vocab_path = "data/stage2_vocab.txt"
            print("🔗 Using Stage 2 reasoning model")
        elif os.path.exists("models_out/stage1_base_language.json"):
            model_path = "models_out/stage1_base_language.json"
            vocab_path = "data/stage1_vocab.txt"
            print("🧠 Using Stage 1 base language model")
        else:
            print("❌ No trained models found! Run training stages first.")
            return
        
        # Launch chat
        agent = UnifiedChatAgent(model_path=model_path, vocab_path=vocab_path)
        agent.interactive_chat()
    
    def train_full_pipeline(self):
        """Run complete sequential training pipeline."""
        print("🚀 IMPROVED SEQUENTIAL TRAINING PIPELINE")
        print("=" * 80)
        print("📋 Running all 4 stages in sequence")
        print()
        
        start_time = time.time()
        
        try:
            # Stage 1: Base Language
            self.stage_1_base_language()
            
            # Stage 2: Reasoning
            self.stage_2_reasoning_finetuning()
            
            # Stage 3: GAN Refinement
            self.stage_3_gan_refinement()
            
            # Final summary
            total_time = time.time() - start_time
            print("🎉 FULL PIPELINE COMPLETE!")
            print("=" * 80)
            print(f"⏱️ Total Time: {total_time:.1f}s ({total_time/60:.1f} minutes)")
            print("📊 Models Available:")
            print("  🧠 Stage 1: models_out/stage1_base_language.json")
            print("  🔗 Stage 2: models_out/stage2_reasoning.json")
            print("  🎭 Stage 3: models_out/stage3_final_generator.json")
            print()
            print("🚀 Ready to chat: python train_improved.py --chat")
            print("=" * 80)
            
        except Exception as e:
            print(f"❌ Pipeline failed: {e}")
            import traceback
            traceback.print_exc()

    # ========== HELPER METHODS ==========

    def _load_base_language_data(self):
        """Load and process general language data from raw_text.txt."""
        examples = []

        if not os.path.exists("data/raw_text.txt"):
            print("❌ data/raw_text.txt not found!")
            return examples

        print("📖 Processing raw_text.txt for base language training...")

        with open("data/raw_text.txt", 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()

        processed = 0
        target_examples = self.config.get('max_base_examples', 2000)

        for line in lines:
            if processed >= target_examples:
                break

            line = line.strip()
            if len(line) > 20 and len(line) < 200:  # Good length sentences
                words = line.split()
                if len(words) >= 5:  # Minimum sentence length
                    # Create input-target pairs for language modeling
                    for i in range(3, min(len(words), 10)):
                        if processed >= target_examples:
                            break
                        input_text = ' '.join(words[:i])
                        target_text = words[i]
                        examples.append(f"{input_text}\t{target_text}")
                        processed += 1

        print(f"✅ Processed {len(examples)} base language examples")
        return examples

    def _load_reasoning_data(self):
        """Load and process structured reasoning data from reasoning_raw.txt."""
        examples = []

        if not os.path.exists("data/reasoning_raw.txt"):
            print("❌ data/reasoning_raw.txt not found!")
            return examples

        print("📖 Processing reasoning_raw.txt for reasoning training...")

        with open("data/reasoning_raw.txt", 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # Define reasoning patterns to extract
        patterns = [
            (r'Given: (.+?)\. Therefore: (.+?)\.', 'given_therefore'),
            (r'If (.+?)\., then (.+?)\.', 'if_then'),
            (r'If (.+?)\., then NOT \((.+?)\.\)', 'if_then_not'),
            (r'Premise: (.+?)\. Contradicts: (.+?)\.', 'premise_contradicts'),
            (r'Given: (.+?)\. Possibly: (.+?)\.', 'given_possibly'),
            (r"From '(.+?)' we cannot conclude '(.+?)'", 'cannot_conclude'),
        ]

        processed = 0
        target_examples = self.config.get('max_reasoning_examples', 1000)

        for pattern, pattern_type in patterns:
            if processed >= target_examples:
                break

            matches = re.findall(pattern, content, re.IGNORECASE)

            for match in matches:
                if processed >= target_examples:
                    break

                premise, conclusion = match

                # Create structured reasoning pairs
                if pattern_type == 'given_therefore':
                    input_text = f"Given: {premise}"
                    target_text = f"Therefore: {conclusion}"
                elif pattern_type == 'if_then':
                    input_text = f"If {premise}"
                    target_text = f"then {conclusion}"
                elif pattern_type == 'if_then_not':
                    input_text = f"If {premise}"
                    target_text = f"then NOT {conclusion}"
                elif pattern_type == 'premise_contradicts':
                    input_text = f"Premise: {premise}"
                    target_text = f"Contradicts: {conclusion}"
                elif pattern_type == 'given_possibly':
                    input_text = f"Given: {premise}"
                    target_text = f"Possibly: {conclusion}"
                elif pattern_type == 'cannot_conclude':
                    input_text = f"From {premise}"
                    target_text = f"we cannot conclude {conclusion}"

                examples.append(f"{input_text}\t{target_text}")
                processed += 1

        print(f"✅ Processed {len(examples)} reasoning examples")
        return examples

    def _build_vocabulary(self, examples, vocab_name):
        """Build vocabulary from examples."""
        print(f"📚 Building {vocab_name} vocabulary...")

        tokenizer = Tokenizer()

        # Extract all text from examples
        all_text = []
        for example in examples:
            parts = example.split('\t')
            all_text.extend(parts)

        # Build vocabulary
        tokenizer.build_vocab(
            all_text,
            min_freq=self.config.get('vocab_min_freq', 1),
            max_size=self.config.get('vocab_size', 5000)
        )

        print(f"✅ Built vocabulary: {tokenizer.get_vocab_size()} tokens")
        return tokenizer

    def _prepare_sequences(self, examples, data_type, extend_vocab=False):
        """Convert examples to token sequences."""
        print(f"🔄 Converting {data_type} examples to sequences...")

        sequences = []

        for example in examples:
            try:
                input_text, target_text = example.split('\t')

                # Encode with current vocabulary
                input_ids = self.tokenizer.encode(
                    input_text,
                    add_bos=True,
                    add_eos=False,
                    max_len=self.config.get('max_seq_len', 32) // 2
                )
                target_ids = self.tokenizer.encode(
                    target_text,
                    add_bos=False,
                    add_eos=True,
                    max_len=self.config.get('max_seq_len', 32) // 2
                )

                if input_ids and target_ids:
                    full_sequence = input_ids + target_ids
                    if len(full_sequence) >= 2:
                        sequences.append(full_sequence)

            except Exception as e:
                continue

        print(f"✅ Created {len(sequences)} sequences from {data_type}")
        return sequences
